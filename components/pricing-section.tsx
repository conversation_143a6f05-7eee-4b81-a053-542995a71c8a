"use client"

import { useState } from "react"
import { CheckCircle2 } from "lucide-react"
import Link from "next/link"

function Feature({ children }: { children: React.ReactNode }) {
  return (
    <li className="flex items-start gap-3">
      <CheckCircle2 className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
      <span>{children}</span>
    </li>
  )
}

export default function PricingSection() {
  const [yearly, setYearly] = useState(false)

  const proPrice = yearly ? 79.9 : 7.99 // $79.90/yr ~= 17% off ($6.66/mo effective)

  return (
    <section id="pricing" className="w-full bg-background">
      <div className="mx-auto max-w-6xl px-6 py-20">
        {/* Headline */}
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-4xl font-semibold tracking-tight text-foreground sm:text-5xl">
            Simple, transparent pricing
          </h2>
          <p className="mt-3 text-muted-foreground">
            Start free. Upgrade when you're ready for more squads, trips, and AI power.
          </p>
        </div>

        {/* Billing Toggle */}
        <div className="mt-8 flex items-center justify-center gap-2">
          <button
            onClick={() => setYearly(false)}
            className={`rounded-full px-4 py-1.5 text-sm font-medium transition ${
              !yearly
                ? "bg-primary text-primary-foreground"
                : "bg-muted text-muted-foreground hover:bg-muted/80"
            }`}
            aria-pressed={!yearly}
          >
            Monthly
          </button>
          <button
            onClick={() => setYearly(true)}
            className={`rounded-full px-4 py-1.5 text-sm font-medium transition ${
              yearly
                ? "bg-primary text-primary-foreground"
                : "bg-muted text-muted-foreground hover:bg-muted/80"
            }`}
            aria-pressed={yearly}
          >
            Yearly <span className="ml-1 opacity-80">(Save 17%)</span>
          </button>
        </div>

        {/* Cards */}
        <div className="mt-10 grid gap-6 sm:grid-cols-2">
          {/* Free plan */}
          <div className="rounded-2xl border border-border bg-card shadow-sm">
            <div className="border-b border-border p-6">
              <p className="text-sm font-medium uppercase tracking-wide text-muted-foreground">
                Free Plan
              </p>
              <h3 className="mt-1 text-2xl font-semibold text-card-foreground">
                Basic features for personal use
              </h3>
              <p className="mt-4 text-3xl font-bold text-card-foreground">$0</p>
            </div>
            <ul className="space-y-3 p-6 text-card-foreground">
              <Feature>Up to 1 squad</Feature>
              <Feature>+1 squad coming soon</Feature>
              <Feature>Up to 2 trips per squad</Feature>
              <Feature>Limited to 3 AI requests</Feature>
              <Feature>Trip chat (view only)</Feature>
              <Feature>Basic trip planning</Feature>
            </ul>
            <div className="p-6">
              <Link
                href="/signup"
                className="inline-flex w-full items-center justify-center rounded-lg bg-primary px-4 py-2.5 text-primary-foreground shadow hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-colors"
              >
                Get started free
              </Link>
            </div>
          </div>

          {/* Pro plan */}
          <div className="relative rounded-2xl border border-border bg-card shadow-sm ring-2 ring-primary">
            <div className="absolute -top-3 right-6 rounded-full bg-primary px-3 py-1 text-xs font-semibold text-primary-foreground shadow">
              Most Popular
            </div>
            <div className="border-b border-border p-6">
              <p className="text-sm font-medium uppercase tracking-wide text-muted-foreground">
                Pro Plan {yearly ? "(Yearly)" : "(Monthly)"}
              </p>
              <h3 className="mt-1 text-2xl font-semibold text-card-foreground">
                Advanced features for frequent travelers
              </h3>
              <div className="mt-4 flex items-baseline gap-2">
                <p className="text-3xl font-bold text-card-foreground">${proPrice.toFixed(2)}</p>
                <span className="text-sm text-muted-foreground">{yearly ? "/year" : "/month"}</span>
              </div>
              {yearly && (
                <p className="mt-1 text-sm text-muted-foreground">
                  Effective ~${(proPrice / 12).toFixed(2)}/mo
                </p>
              )}
            </div>
            <ul className="space-y-3 p-6 text-card-foreground">
              <Feature>Up to 5 squads</Feature>
              <Feature>Up to 3 trips per squad</Feature>
              <Feature>Unlimited AI requests</Feature>
              <Feature>Trip chat messaging</Feature>
              <Feature>Advanced trip planning</Feature>
              <Feature>Priority support</Feature>
            </ul>
            <div className="p-6">
              <Link
                href="/login?callback=%2Fsettings%3Ftab%3Dbilling"
                className="inline-flex w-full items-center justify-center rounded-lg bg-gradient-to-br from-[#FFD54F] to-[#FFB300] hover:from-[#FFCC02] hover:to-[#FF8F00] px-4 py-2.5 font-medium text-[#FFF8DC] [text-shadow:1px_1px_2px_rgba(0,0,0,0.5)] shadow transition-all duration-300 ease-in-out hover:scale-105 active:scale-95"
              >
                Upgrade to Pro
              </Link>
              <p className="mt-2 text-center text-xs text-muted-foreground">
                Billed {yearly ? "annually" : "monthly"}. Cancel anytime.
              </p>
            </div>
          </div>
        </div>

        {/* Footnote */}
        <p className="mt-8 text-center text-sm text-muted-foreground">
          Prices in USD. Taxes may apply. Features and limits may change as we improve Togeda.ai.
        </p>
      </div>
    </section>
  )
}
