"use client"

import React from "react"
import { Badge } from "@/components/ui/badge"
import { Lightbulb } from "lucide-react"
import { cn } from "@/lib/utils"

interface UserSuggestedBadgeProps {
  className?: string
  variant?: "default" | "secondary" | "outline"
}

export function UserSuggestedBadge({ className, variant = "secondary" }: UserSuggestedBadgeProps) {
  return (
    <Badge
      variant={variant}
      className={cn(
        "flex items-center gap-1 text-xs font-medium",
        // Light mode: white background with teal text
        "bg-white text-[#00796B] border border-[#00796B]/20",
        // Dark mode: white background with teal text (same for consistency)
        "dark:bg-white dark:text-[#00796B] dark:border-[#00796B]/20",
        className
      )}
    >
      <Lightbulb className="h-3 w-3" />
      User-Suggested
    </Badge>
  )
}
