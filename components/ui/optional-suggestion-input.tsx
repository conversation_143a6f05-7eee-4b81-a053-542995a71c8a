"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { ChevronDown, ChevronUp, X, Lightbulb, Crown } from "lucide-react"
import { cn } from "@/lib/utils"

interface OptionalSuggestionInputProps {
  value: string
  onChange: (value: string) => void
  placeholder: string
  label?: string
  maxLength?: number
  disabled?: boolean
  className?: string
  isProUser?: boolean
}

export function OptionalSuggestionInput({
  value,
  onChange,
  placeholder,
  label = "Additional Suggestions",
  maxLength = 100,
  disabled = false,
  className,
  isProUser = false,
}: OptionalSuggestionInputProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const remainingChars = maxLength - value.length

  // Don't render for non-Pro users
  if (!isProUser) {
    return null
  }

  const handleClear = () => {
    onChange("")
  }

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded)
  }

  return (
    <div className={cn("space-y-2", className)}>
      {/* Toggle Button */}
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={toggleExpanded}
        disabled={disabled}
        className="w-full justify-between text-left h-auto py-2 px-3 border border-dashed border-muted-foreground/30 hover:border-muted-foreground/50 hover:bg-muted/30"
      >
        <div className="flex items-center gap-2">
          <Lightbulb className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">
            {label} <span className="text-xs">(Optional)</span>
          </span>
          <Crown className="h-3 w-3 text-[#FFD54F]" />
        </div>
        {isExpanded ? (
          <ChevronUp className="h-4 w-4 text-muted-foreground" />
        ) : (
          <ChevronDown className="h-4 w-4 text-muted-foreground" />
        )}
      </Button>

      {/* Expanded Input Section */}
      {isExpanded && (
        <div className="space-y-2 p-3 border border-muted-foreground/20 rounded-md bg-muted/10">
          <div className="flex items-center justify-between">
            <Label htmlFor="optional-suggestion" className="text-sm font-medium">
              {label}
            </Label>
            <Badge variant="secondary" className="text-xs">
              Pro Feature
            </Badge>
          </div>

          <div className="relative">
            <Textarea
              id="optional-suggestion"
              value={value}
              onChange={(e) => onChange(e.target.value)}
              placeholder={placeholder}
              maxLength={maxLength}
              disabled={disabled}
              className="min-h-[80px] resize-none pr-8"
              rows={3}
            />

            {/* Clear Button */}
            {value && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleClear}
                disabled={disabled}
                className="absolute top-2 right-2 h-6 w-6 p-0 hover:bg-muted"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>

          {/* Character Counter */}
          <div className="flex justify-between items-center text-xs text-muted-foreground">
            <span>Help the AI understand what you're looking for specifically</span>
            <span
              className={cn(
                remainingChars < 10 && "text-orange-500",
                remainingChars === 0 && "text-red-500"
              )}
            >
              {remainingChars}/{maxLength}
            </span>
          </div>
        </div>
      )}
    </div>
  )
}
