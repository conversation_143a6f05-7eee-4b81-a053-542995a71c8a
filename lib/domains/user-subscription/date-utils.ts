import { Timestamp } from "firebase/firestore"

/**
 * Utility functions for handling date conversions in subscription data
 *
 * This helps ensure consistent date handling across the subscription system,
 * particularly when dealing with different date formats from Stripe, Firestore, and JavaScript.
 */

/**
 * Converts various date formats to a JavaScript Date object
 *
 * Handles:
 * - JavaScript Date objects (returns as-is)
 * - Unix timestamps (numbers) from Stripe (converts from seconds to milliseconds)
 * - Firestore Timestamp objects (calls .toDate())
 * - Date strings (attempts to parse)
 *
 * @param dateValue - The date value to convert
 * @param fieldName - Optional field name for better error logging
 * @returns JavaScript Date object or null if conversion fails
 */
export function convertToDate(dateValue: any, fieldName?: string): Date | null {
  if (!dateValue) {
    return null
  }

  try {
    // Already a Date object
    if (dateValue instanceof Date) {
      return dateValue
    }

    // Unix timestamp from Stripe (in seconds)
    if (typeof dateValue === "number") {
      // If timestamp is in seconds (before year 2033), convert to milliseconds
      const milliseconds = dateValue < 2000000000 ? dateValue * 1000 : dateValue
      return new Date(milliseconds)
    }

    // Firestore Timestamp object
    if (
      dateValue &&
      typeof dateValue === "object" &&
      typeof (dateValue as any).toDate === "function"
    ) {
      return (dateValue as any).toDate()
    }

    // Attempt to parse as date string or other format
    const parsed = new Date(dateValue)
    if (isNaN(parsed.getTime())) {
      throw new Error(`Invalid date value: ${dateValue}`)
    }
    return parsed
  } catch (error) {
    console.warn(`Unable to convert ${fieldName || "date value"} to Date:`, dateValue, error)
    return null
  }
}

/**
 * Safely converts a subscription's currentPeriodEnd to a Date
 *
 * @param currentPeriodEnd - The currentPeriodEnd value from subscription data
 * @returns JavaScript Date object or null if conversion fails
 */
export function convertCurrentPeriodEnd(currentPeriodEnd: any): Date | null {
  return convertToDate(currentPeriodEnd, "currentPeriodEnd")
}

/**
 * Safely converts a subscription's endDate to a Date
 *
 * @param endDate - The endDate value from subscription data
 * @returns JavaScript Date object or null if conversion fails
 */
export function convertEndDate(endDate: any): Date | null {
  return convertToDate(endDate, "endDate")
}

/**
 * Safely converts an appliedAt timestamp to a Date
 *
 * @param appliedAt - The appliedAt value from subscription data
 * @returns JavaScript Date object or null if conversion fails
 */
export function convertAppliedAt(appliedAt: any): Date | null {
  return convertToDate(appliedAt, "appliedAt")
}

/**
 * Checks if a subscription period is still valid (not expired)
 *
 * @param currentPeriodEnd - The currentPeriodEnd value from subscription data
 * @returns true if the period is still valid, false if expired, true if unable to determine (safe default)
 */
export function isSubscriptionPeriodValid(currentPeriodEnd: any): boolean {
  const endDate = convertCurrentPeriodEnd(currentPeriodEnd)

  if (!endDate) {
    // If we can't parse the date, default to valid to avoid blocking users
    return true
  }

  return endDate > new Date()
}

/**
 * Checks if a subscription has expired based on its endDate
 *
 * @param endDate - The endDate value from subscription data
 * @returns true if expired, false if still valid, false if unable to determine (safe default)
 */
export function isSubscriptionExpired(endDate: any): boolean {
  const date = convertEndDate(endDate)

  if (!date) {
    // If we can't parse the date, default to not expired to avoid blocking users
    return false
  }

  return date <= new Date()
}
