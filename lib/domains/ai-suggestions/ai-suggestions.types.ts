"use client"

import { AIUsageCategory } from "../user-ai-usage/user-ai-usage.types"
import { SubscriptionErrorType } from "../user-subscription/user-subscription.types"
import {
  TripSuggestion,
  TaskSuggestion,
  ItinerarySuggestion,
  AffiliateLink,
} from "@/lib/api-client"
import { TaggedAffiliateLink } from "@/lib/affiliate-links-map"
import {
  CachedTripSuggestion,
  CachedTaskSuggestion,
  CachedItinerarySuggestion,
} from "./ai-suggestions-cache.service"

/**
 * Base interface for AI suggestion hook returns
 */
export interface BaseAISuggestionsHookReturn<T> {
  /** The suggestions returned from the AI */
  suggestions: T[]
  /** Whether a request is in progress */
  loading: boolean
  /** Error object for usage limit errors */
  usageError: {
    show: boolean
    errorType: SubscriptionErrorType
    usageData: {
      daily: number
      weekly: number
      dailyLimit: number
      weeklyLimit: number
      categoryCount?: number
      categoryLimit?: number
    }
  } | null
  /** Generic error object for API failures */
  error: string | null
  /** Whether cached suggestions are being used */
  usingCachedSuggestions: boolean
  /** Function to load suggestions */
  loadSuggestions: (refresh?: boolean) => Promise<void>
  /** Function to check if the user can make a request */
  canMakeRequest: (category: AIUsageCategory) => Promise<boolean>
}

/**
 * Trip suggestions hook return type
 */
export interface TripSuggestionsHookReturn
  extends BaseAISuggestionsHookReturn<CachedTripSuggestion> {
  /** Whether suggestions have been loaded */
  suggestionsLoaded: boolean
}

/**
 * Task suggestions hook return type
 */
export interface TaskSuggestionsHookReturn
  extends BaseAISuggestionsHookReturn<CachedTaskSuggestion> {
  /** Whether to show AI suggestions */
  showAiSuggestions: boolean
}

/**
 * Itinerary suggestions hook return type
 */
export interface ItinerarySuggestionsHookReturn
  extends BaseAISuggestionsHookReturn<CachedItinerarySuggestion> {
  /** Whether to show suggestions */
  showSuggestions: boolean
  /** Affiliate links for suggestions */
  affiliateLinks: Record<string, TaggedAffiliateLink | null>
  /** Current user suggestion */
  userSuggestion: string
  /** Function to set user suggestion */
  setUserSuggestion: (suggestion: string) => void
}
