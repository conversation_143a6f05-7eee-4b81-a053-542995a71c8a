// Server-side only Firebase Admin initialization
// Uses dynamic imports to prevent browser issues

import { initializeApp, cert, getApps, getApp } from "firebase-admin/app"
import { getAuth } from "firebase-admin/auth"
import { getFirestore, FieldValue } from "firebase-admin/firestore"

// A promise to hold the initialization state.
let initializePromise: Promise<void> | null = null

// Function to initialize the app and return a promise.
function initializeAppWithPromise() {
  if (initializePromise) {
    return initializePromise
  }

  initializePromise = new Promise<void>((resolve, reject) => {
    try {
      if (getApps().length === 0) {
        if (!process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
          throw new Error("FIREBASE_SERVICE_ACCOUNT_KEY environment variable is not set")
        }
        const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)
        initializeApp({ credential: cert(serviceAccount) })
      }
      resolve()
    } catch (error) {
      reject(error)
    }
  })
  return initializePromise
}

// Direct exports that rely on the promise.
export async function getAdminAuth() {
  await initializeAppWithPromise()
  return getAuth(getApp())
}

export async function getAdminDb() {
  await initializeAppWithPromise()
  return getFirestore(getApp())
}

export async function getAdminFieldValue() {
  await initializeAppWithPromise()
  return FieldValue
}

// Helper function to verify a Firebase ID token
export async function verifyAuthToken(token: string) {
  try {
    const adminAuth = await getAdminAuth()
    const decodedToken = await adminAuth.verifyIdToken(token)
    return {
      uid: decodedToken.uid,
      email: decodedToken.email,
      firebase_sign_in_provider: decodedToken.firebase?.sign_in_provider,
      isValid: true,
    }
  } catch (error) {
    console.error("Error verifying auth token:", error)
    return { isValid: false }
  }
}
