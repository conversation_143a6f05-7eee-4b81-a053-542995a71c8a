import { Timestamp } from "firebase-admin/firestore"
import { Squad, SquadMember } from "@/lib/domains/squad/squad.types"
import { User } from "@/lib/domains/user/user.types"
import { UserServerService } from "../user/user.service"
import { getAdminDb } from "@/lib/firebase-admin"

/**
 * Server-side Squad Service using Firebase Admin SDK
 */
export class SquadServerService {
  private static readonly COLLECTION = "squads"
  private static readonly USER_SQUADS_SUBCOLLECTION = "squads"

  /**
   * Serialize Firestore Timestamps to plain objects for SSR
   */
  private static serializeTimestamps(data: any): any {
    if (!data) return data

    if (data instanceof Timestamp) {
      return data.toDate()
    }

    if (Array.isArray(data)) {
      return data.map((item) => this.serializeTimestamps(item))
    }

    if (typeof data === "object" && data !== null) {
      const serialized: any = {}
      for (const [key, value] of Object.entries(data)) {
        serialized[key] = this.serializeTimestamps(value)
      }
      return serialized
    }

    return data
  }

  /**
   * Get a squad by ID
   * @param squadId Squad ID
   * @returns The squad data or null if not found
   */
  static async getSquad(squadId: string): Promise<Squad | null> {
    try {
      const adminDb = await getAdminDb()
      const squadDoc = await adminDb.collection(this.COLLECTION).doc(squadId).get()

      if (squadDoc.exists) {
        const squadData = squadDoc.data()
        const serializedData = this.serializeTimestamps(squadData)
        return { ...serializedData, id: squadId } as Squad
      }

      return null
    } catch (error) {
      console.error("Error getting squad (server):", error)
      throw error
    }
  }

  /**
   * Get squads for a user
   * @param userId User ID
   * @returns Array of squads with member details
   */
  static async getUserSquads(userId: string): Promise<Squad[]> {
    try {
      const adminDb = await getAdminDb()

      // Query user's squad subcollection
      const userSquadsSnapshot = await adminDb
        .collection("users")
        .doc(userId)
        .collection(this.USER_SQUADS_SUBCOLLECTION)
        .get()

      if (userSquadsSnapshot.empty) {
        return []
      }

      // Get squad details for each user squad
      const squads: Squad[] = []
      const squadPromises = userSquadsSnapshot.docs.map(async (userSquadDoc: any) => {
        const userSquad = userSquadDoc.data()
        const squadDoc = await adminDb.collection(this.COLLECTION).doc(userSquad.squadId).get()

        if (squadDoc.exists) {
          const squadData = squadDoc.data()
          const serializedData = this.serializeTimestamps(squadData)
          return { ...serializedData, id: squadDoc.id } as Squad
        }
        return null
      })

      const squadResults = await Promise.all(squadPromises)
      squadResults.forEach((squad: any) => {
        if (squad) squads.push(squad)
      })

      return squads
    } catch (error) {
      console.error("Error getting user squads (server):", error)
      throw error
    }
  }

  /**
   * Get squad members with user details
   * @param squadId Squad ID
   * @returns Array of squad members with user details
   */
  static async getSquadMembersWithDetails(
    squadId: string
  ): Promise<(SquadMember & { user: User })[]> {
    try {
      const adminDb = await getAdminDb()

      // Get squad members
      const membersSnapshot = await adminDb
        .collection(this.COLLECTION)
        .doc(squadId)
        .collection("members")
        .get()

      if (membersSnapshot.empty) {
        return []
      }

      // Get user IDs
      const userIds = membersSnapshot.docs.map((doc: any) => doc.data().userId)

      // Get user details in parallel
      const users = await UserServerService.getUsers(userIds)

      // Combine member data with user details
      const membersWithDetails = membersSnapshot.docs
        .map((doc: any) => {
          const memberData = doc.data() as SquadMember
          const user = users[memberData.userId]

          if (user) {
            return { ...memberData, user }
          }
          return null
        })
        .filter(Boolean) as (SquadMember & { user: User })[]

      return membersWithDetails
    } catch (error) {
      console.error("Error getting squad members with details (server):", error)
      throw error
    }
  }

  /**
   * Get squad leader details
   * @param squadId Squad ID
   * @returns Squad leader user data or null
   */
  static async getSquadLeader(squadId: string): Promise<User | null> {
    try {
      const squad = await this.getSquad(squadId)
      if (!squad?.leaderId) {
        return null
      }

      return await UserServerService.getUser(squad.leaderId)
    } catch (error) {
      console.error("Error getting squad leader (server):", error)
      throw error
    }
  }

  /**
   * Get multiple squads with their leaders and member counts
   * @param squadIds Array of squad IDs
   * @returns Record of squad ID to squad data with leader info
   */
  static async getSquadsWithLeaders(
    squadIds: string[]
  ): Promise<Record<string, Squad & { leader?: User }>> {
    try {
      if (squadIds.length === 0) return {}

      const adminDb = await getAdminDb()
      const squads: Record<string, Squad & { leader?: User }> = {}

      // Get all squads in parallel
      const squadPromises = squadIds.map((squadId) =>
        adminDb.collection(this.COLLECTION).doc(squadId).get()
      )

      const squadDocs = await Promise.all(squadPromises)

      // Extract leader IDs
      const leaderIds: string[] = []
      const squadData: Record<string, Squad> = {}

      squadDocs.forEach((doc: any, index: number) => {
        if (doc.exists) {
          const data = doc.data() as Squad
          const serializedData = this.serializeTimestamps(data)
          const squadId = squadIds[index]
          squadData[squadId] = { ...serializedData, id: squadId }

          if (data.leaderId) {
            leaderIds.push(data.leaderId)
          }
        }
      })

      // Get all leaders in parallel
      const leaders = await UserServerService.getUsers(leaderIds)

      // Combine squad data with leader info
      Object.entries(squadData).forEach(([squadId, squad]) => {
        squads[squadId] = {
          ...squad,
          leader: squad.leaderId ? leaders[squad.leaderId] : undefined,
        }
      })

      return squads
    } catch (error) {
      console.error("Error getting squads with leaders (server):", error)
      throw error
    }
  }
}
