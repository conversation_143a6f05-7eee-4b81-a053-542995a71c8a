import { User } from "@/lib/domains/user/user.types"

/**
 * Server-side authentication context
 */
export interface ServerAuthContext {
  user: User | null
  isAuthenticated: boolean
  userId: string | null
}

/**
 * Authentication result for server-side operations
 */
export interface ServerAuthResult {
  success: boolean
  user?: User
  error?: string
}

/**
 * Token verification result
 */
export interface TokenVerificationResult {
  valid: boolean
  userId?: string
  error?: string
}
