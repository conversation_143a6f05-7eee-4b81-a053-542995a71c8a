/**
 * Server-side Trip Domain Exports
 *
 * This module provides server-side trip functionality using Firebase Admin SDK.
 * All functions require explicit userId parameters since they cannot access
 * client-side auth stores.
 */

export { TripServerService } from "./trip.service"

// Re-export types from the client-side domain for convenience
export type {
  Trip,
  TripStatus,
  TripCreateData,
  TripUpdateData,
  TripFormData,
  GooglePlaceImage,
  TripImageAttribution,
  TripsPerSquad,
} from "@/lib/domains/trip/trip.types"
