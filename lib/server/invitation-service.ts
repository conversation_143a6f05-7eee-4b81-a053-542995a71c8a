import { getAdminDb } from "../firebase-admin"

// Types
export interface Invitation {
  id: string
  squadId: string
  squadName: string
  inviterId: string
  inviterName: string
  inviteeId: string
  inviteeEmail: string
  status: "pending" | "accepted" | "rejected"
  createdAt: any
  lastUpdated?: any
  lastResent?: any
}

/**
 * Get an invitation by ID using the Firebase Admin SDK
 * This is for server-side use only
 */
export async function getInvitationById(invitationId: string): Promise<Invitation | null> {
  try {
    // Get the admin Firestore instance
    const adminDb = await getAdminDb()

    // Get the invitation document
    const invitationDoc = await adminDb.collection("invitations").doc(invitationId).get()

    if (!invitationDoc.exists) {
      return null
    }

    // Return the invitation data
    return { ...invitationDoc.data(), id: invitationDoc.id } as Invitation
  } catch (error) {
    console.error("Error getting invitation (server-side):", error)
    throw error
  }
}
