"use client"

import { User } from "firebase/auth"

/**
 * Client-side service to manage auth cookies for SSR
 */
export class AuthCookieService {
  /**
   * Set Firebase token in cookie for server-side access
   * Uses the login API endpoint to set HttpOnly cookie securely
   */
  static async setAuth<PERSON><PERSON>ie(user: User | null) {
    try {
      if (user) {
        const token = await user.getIdToken()

        // Call the login API to set the HttpOnly cookie
        const response = await fetch("/api/auth/login", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ idToken: token }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || "Failed to set auth cookie")
        }

        // Also set a client-side accessible cookie for immediate client-side checks
        // document.cookie = `firebase-auth-token-client=${token}; path=/; max-age=${60 * 60 * 24 * 7}; samesite=lax${
        //   process.env.NODE_ENV === "production" ? "; secure" : ""
        // }`
      } else {
        // Clear cookies on logout
        // document.cookie =
        //   "firebase-auth-token-client=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT"

        // Call logout endpoint to clear HttpOnly cookie
        try {
          await fetch("/api/auth/logout", {
            method: "POST",
          })
        } catch (logoutError) {
          console.error("Error calling logout endpoint:", logoutError)
        }
      }
    } catch (error) {
      console.error("Error setting auth cookie:", error)
      throw error
    }
  }

  /**
   * Clear auth cookie
   */
  static clearAuthCookie() {
    // document.cookie = "firebase-auth-token-client=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT"

    // Call logout endpoint to clear HttpOnly cookie
    fetch("/api/auth/logout", {
      method: "POST",
    }).catch((error) => {
      console.error("Error calling logout endpoint:", error)
    })
  }

  /**
   * Refresh auth cookie with new token
   */
  static async refreshAuthCookie(user: User) {
    try {
      const token = await user.getIdToken(true) // Force refresh

      // Call the login API to refresh the HttpOnly cookie
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ idToken: token }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to refresh auth cookie")
      }

      // Also refresh the client-side cookie
      // document.cookie = `firebase-auth-token-client=${token}; path=/; max-age=${60 * 60 * 24 * 7}; samesite=lax${
      //   process.env.NODE_ENV === "production" ? "; secure" : ""
      // }`
    } catch (error) {
      console.error("Error refreshing auth cookie:", error)
      throw error
    }
  }
}
