import type { Trip } from "./domains/trip/trip.types"
import type { User } from "./domains/user/user.types"
import type { UserPreferences } from "./domains/user-preferences/user-preferences.types"
import { auth } from "./firebase"
import { ReactNode } from "react"
import { create<PERSON><PERSON><PERSON><PERSON>, with<PERSON><PERSON> } from "./cache-utils"

// Constants for API timeouts and caching
const API_TIMEOUT_MS = 30000 // 10 seconds timeout for API calls
const CACHE_EXPIRATION_MS = 30 * 60 * 1000 // 30 minutes cache expiration

// Define types for our suggestion systems
export interface TaskSuggestion {
  title: string
  description: string
  category: "planning" | "booking" | "preparation" | "coordination" | "during-trip"
  priority: "high" | "medium" | "low"
  tags?: string[]
  hasAffiliateLink?: boolean
  affiliateLink?: AffiliateLink
}

export interface TripSuggestion {
  destination: string
  description: string
  tags: string[]
  budget: string
  imageUrl?: string
  placeId?: string
}

export interface ActivitySuggestion {
  title: string
  description: string
  cost: string
  duration: string
}

export interface ItinerarySuggestion {
  title: string
  description: string
  day: number
  timeOfDay: "morning" | "afternoon" | "evening"
  duration: string
  location?: string // Primary location string for easy copying
  tags?: string[]
  // Google Places integration
  googlePlaces?: {
    place_id: string
    rating?: number
    price_level?: number // 0-4 scale (0=Free, 1=$, 2=$$, 3=$$$, 4=$$$$)
    user_ratings_total?: number
    formatted_address?: string
    phone_number?: string
    website?: string
    opening_hours?: {
      open_now?: boolean
      weekday_text?: string[]
    }
    photos?: Array<{
      photo_reference: string
      width: number
      height: number
    }>
    types?: string[] // Google Places types (restaurant, tourist_attraction, etc.)
    geometry?: {
      location: {
        lat: number
        lng: number
      }
    }
  }
}

export interface AffiliateLink {
  [x: string]: ReactNode
  url: string
  title: string
  description: string
  provider?: string
  tags?: string[]
}

// Helper function to get the authentication token
async function getAuthToken() {
  try {
    const currentUser = auth.currentUser
    if (!currentUser) {
      throw new Error("User not authenticated")
    }

    const token = await currentUser.getIdToken()
    return token
  } catch (error) {
    console.error("Error getting auth token:", error)
    throw error
  }
}

// Helper function to make authenticated API requests with timeout
async function fetchWithAuth(url: string, options: RequestInit = {}) {
  try {
    const token = await getAuthToken()

    // Add the authorization header
    const headers = new Headers(options.headers || {})
    headers.set("Authorization", `Bearer ${token}`)
    headers.set("Content-Type", "application/json")

    // Create an AbortController for timeout handling
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT_MS)

    // Make the request with the authorization header and timeout
    const response = await fetch(url, {
      ...options,
      headers,
      signal: controller.signal,
    })

    // Clear the timeout
    clearTimeout(timeoutId)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: "Unknown error" }))
      throw new Error(errorData.error || `API error: ${response.status}`)
    }

    return response
  } catch (error) {
    if (error instanceof DOMException && error.name === "AbortError") {
      console.error(`Request to ${url} timed out after ${API_TIMEOUT_MS}ms`)
      throw new Error(`Request timed out. Please try again.`)
    }
    console.error(`Error fetching ${url}:`, error)
    throw error
  }
}

// Client-side API wrappers for OpenAI functions
export async function generateTripSuggestions(
  preferences: any,
  previousSuggestions?: TripSuggestion[],
  locationPreference?: "local" | "national" | "global",
  userLocation?: { location: string; locationPlaceId: string },
  userSuggestion?: string
): Promise<TripSuggestion[]> {
  try {
    const response = await fetchWithAuth("/api/ai", {
      method: "POST",
      body: JSON.stringify({
        type: "trip_suggestions",
        data: {
          preferences,
          previousSuggestions,
          locationPreference,
          userLocation,
          userSuggestion,
        },
      }),
    })

    const data = await response.json()
    return data.result
  } catch (error) {
    console.error("Error generating trip suggestions:", error)
    throw error
  }
}

export async function generateItinerary(tripDetails: any): Promise<string> {
  try {
    const response = await fetchWithAuth("/api/ai", {
      method: "POST",
      body: JSON.stringify({
        type: "itinerary",
        data: {
          tripDetails,
        },
      }),
    })

    const data = await response.json()
    return data.result
  } catch (error) {
    console.error("Error generating itinerary:", error)
    throw error
  }
}

export async function processAIChatMessage(message: string, chatHistory: any[]): Promise<string> {
  try {
    const response = await fetchWithAuth("/api/ai", {
      method: "POST",
      body: JSON.stringify({
        type: "chat_message",
        data: {
          message,
          chatHistory,
        },
      }),
    })

    const data = await response.json()
    return data.result
  } catch (error) {
    console.error("Error processing AI chat message:", error)
    throw error
  }
}

export async function generateTaskSuggestions(
  trip: Trip,
  userPreferences: Partial<UserPreferences>,
  userLocation?: string,
  existingTasks: string[] = []
): Promise<TaskSuggestion[]> {
  try {
    const response = await fetchWithAuth("/api/ai", {
      method: "POST",
      body: JSON.stringify({
        type: "task_suggestions",
        data: {
          trip,
          userPreferences,
          userLocation,
          existingTasks,
        },
      }),
    })

    const data = await response.json()
    return data.result
  } catch (error) {
    console.error("Error generating task suggestions:", error)
    throw error
  }
}

// New optimized function that combines task suggestions and affiliate links in one request
export async function generateTaskSuggestionsWithAffiliateLinks(
  trip: Trip,
  userPreferences: Partial<User>,
  existingTasks: string[] = [],
  userLocation?: string
): Promise<{
  suggestions: TaskSuggestion[]
  affiliateLinks: Record<string, AffiliateLink | null>
}> {
  try {
    // Create a cache key based on the function parameters
    const params = {
      tripId: trip.id,
      destination: trip.destination,
      startDate: trip.startDate,
      endDate: trip.endDate,
      existingTasksCount: existingTasks.length,
      userLocation,
    }
    const cacheKey = createCacheKey("task_suggestions_with_links", params)

    // Use the withCache utility to handle caching
    return await withCache(
      cacheKey,
      async () => {
        const response = await fetchWithAuth("/api/ai", {
          method: "POST",
          body: JSON.stringify({
            type: "task_suggestions_with_links",
            data: {
              trip,
              userPreferences,
              existingTasks,
              userLocation,
            },
          }),
        })

        const data = await response.json()
        return data.result
      },
      { expirationMs: CACHE_EXPIRATION_MS }
    )
  } catch (error) {
    console.error("Error generating task suggestions with affiliate links:", error)
    throw error
  }
}

export async function generateTaskCompletionSuggestions(
  taskTitle: string,
  taskDescription: string,
  tripDestination: string
): Promise<{ suggestion: string; affiliateLink: AffiliateLink | null }[]> {
  try {
    const response = await fetchWithAuth("/api/ai", {
      method: "POST",
      body: JSON.stringify({
        type: "task_completion_suggestions",
        data: {
          taskTitle,
          taskDescription,
          tripDestination,
        },
      }),
    })

    const data = await response.json()
    return data.result
  } catch (error) {
    console.error("Error generating task completion suggestions:", error)
    throw error
  }
}

export async function suggestAffiliateLinks(
  taskTitle: string,
  taskDescription: string,
  destination: string
): Promise<AffiliateLink | null> {
  try {
    const response = await fetchWithAuth("/api/ai", {
      method: "POST",
      body: JSON.stringify({
        type: "affiliate_links",
        data: {
          taskTitle,
          taskDescription,
          destination,
        },
      }),
    })

    const data = await response.json()
    return data.result
  } catch (error) {
    console.error("Error suggesting affiliate links:", error)
    return null
  }
}

// Note: This function is still used by task suggestions, but itinerary suggestions now use affiliate-links-map.ts

export async function generateDestinationActivities(
  destination: string,
  budget?: string,
  preferences?: string[]
): Promise<ActivitySuggestion[]> {
  try {
    const response = await fetchWithAuth("/api/ai", {
      method: "POST",
      body: JSON.stringify({
        type: "destination_activities",
        data: {
          destination,
          budget,
          preferences,
        },
      }),
    })

    const data = await response.json()
    return data.result
  } catch (error) {
    console.error("Error generating destination activities:", error)
    throw error
  }
}

export async function generateActivitySuggestions(
  trip: Trip,
  userPreferences: Partial<User>,
  day?: number,
  activityPreferences?: any,
  recentSuggestions?: string[],
  userSuggestion?: string
): Promise<ItinerarySuggestion[]> {
  try {
    const response = await fetchWithAuth("/api/ai", {
      method: "POST",
      body: JSON.stringify({
        type: "activity_suggestions",
        data: {
          trip,
          userPreferences,
          day,
          activityPreferences,
          recentSuggestions,
          userSuggestion,
        },
      }),
    })

    const data = await response.json()
    return data.result
  } catch (error) {
    console.error("Error generating activity suggestions:", error)
    throw error
  }
}
