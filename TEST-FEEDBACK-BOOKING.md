# Test Booking for Feedback Feature

## ✅ Test Booking Created and Fixed Successfully

A completed booking has been created for testing the feedback feature.

**IMPORTANT:** The booking has been updated with the required `id` field that was missing initially.

### 📋 Booking Details

- **Booking ID**: `test-booking-*************` ✅ **FIXED WITH CORRECT UID**
- **User**: TEst Account (<EMAIL>)
- **User ID**: `U8TeGBYI2WV4DuBgXF1Ep24eZQF2` ✅ **CORRECT FIREBASE AUTH UID**
- **Experience**: Authentic Italian Cooking Class
- **Experience ID**: `4DSGuwTKIhaJ9fyuhJ0D`
- **Date**: 2025-09-30 (Yesterday)
- **Time**: 14:00
- **Status**: ✅ **completed**
- **Payment Status**: ✅ **paid**
- **Guests**: 2

### 🔗 Testing URLs

**Direct Feedback URL:**

```
http://localhost:3000/experiences/feedback/test-booking-*************
```

**My Bookings Page:**

```
http://localhost:3000/experiences?tab=my-bookings
```

### 🧪 How to Test

1. **Login** as `<EMAIL>`
2. **Navigate** to one of the URLs above
3. **Check browser console** for detailed logging
4. **Verify** the feedback form loads correctly

### 📍 Database Locations

The booking exists in both required locations:

1. **Experience Bookings Collection:**

   ```
   localExperiences/4DSGuwTKIhaJ9fyuhJ0D/bookings/test-booking-*************
   ```

2. **User Bookings Collection:**
   ```
   users/U8TeGBYI2WV4DuBgXF1Ep24eZQF2/localExperienceBookings/test-booking-*************
   ```

### 🔍 What to Look For

The feedback page should:

- ✅ Load without redirecting
- ✅ Show booking details (experience title, date, time)
- ✅ Display the feedback form
- ✅ Allow submission of feedback

### 🐛 Debugging

If the page redirects, check the browser console for:

- Auth state resolution logs
- Booking data loading logs
- Validation failure messages
- Service error messages

### 🧹 Cleanup

To remove the test booking after testing:

```bash
# Delete from user's collection
firebase firestore:delete "users/6oqh095Hj0YcWH1dy8sNhVHouBr1/localExperienceBookings/test-booking-1759345658314" --project brotrip-mvp

# Delete from experience's collection
firebase firestore:delete "localExperiences/4DSGuwTKIhaJ9fyuhJ0D/bookings/test-booking-1759345658314" --project brotrip-mvp
```

Or create another test booking by running:

```bash
node scripts/create-test-booking.js
```

### 📝 Notes

- The booking date is set to yesterday (2025-09-30) to ensure it's in the past
- Status is "completed" which is required for feedback
- Payment status is "paid" which is also required
- All required fields are populated
- The booking follows the exact structure expected by the feedback page
