import { NextRequest, NextResponse } from "next/server"
import { verifyAuth } from "@/lib/api-auth"
import { sendInvitationEmail, generateInvitationLink } from "@/lib/server/email-service"
import { getAdminDb } from "@/lib/firebase-admin"

// Helper function to get invitation data from invitation links only
async function getInvitationData(invitationId: string) {
  try {
    const adminDb = await getAdminDb()

    const invitationLinkDoc = await adminDb.collection("invitation-links").doc(invitationId).get()
    if (invitationLinkDoc.exists) {
      const invitationLinkData = invitationLinkDoc.data()

      // Convert invitation link data to invitation format
      return {
        id: invitationLinkDoc.id,
        squadId: invitationLinkData?.squadId,
        squadName: invitationLinkData?.squadName,
        inviterId: invitationLinkData?.inviterId,
        inviterName: invitationLinkData?.inviterName,
        inviteeId: "", // Not applicable for invitation links
        inviteeEmail: "", // Will be provided by the email being sent to
        status: "pending" as const,
        createdAt: invitationLinkData?.createdAt,
        lastUpdated: null,
        lastResent: null,
      }
    }

    return null
  } catch (error) {
    console.error("Error getting invitation data:", error)
    return null
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = await verifyAuth(request)

    if (!authResult.isAuthenticated) {
      return authResult.response
    }

    // Get the request body
    const body = await request.json()
    const { invitationId, templateId, inviteeEmail, invitationSendId } = body

    if (!invitationId) {
      return NextResponse.json({ error: "Missing required field: invitationId" }, { status: 400 })
    }

    // Get the invitation details from invitation links
    let invitation
    try {
      invitation = await getInvitationData(invitationId)

      if (!invitation) {
        console.log("Invitation not found with ID:", invitationId)
        return NextResponse.json({ error: "Invitation not found" }, { status: 404 })
      }

      // If this is an invitation link (no inviteeEmail), use the provided email
      if (!invitation.inviteeEmail && inviteeEmail) {
        invitation.inviteeEmail = inviteeEmail
      }

      console.log("Successfully retrieved invitation:", {
        id: invitation.id,
        squadId: invitation.squadId,
        inviteeEmail: invitation.inviteeEmail || "N/A",
        status: invitation.status,
      })
    } catch (invitationError) {
      console.error("Error fetching invitation:", invitationError)
      return NextResponse.json(
        {
          error: "Error fetching invitation",
          details: invitationError instanceof Error ? invitationError.message : "Unknown error",
        },
        { status: 500 }
      )
    }

    // Note: Comprehensive invitation validation (squad membership, cooldown, etc.) is now handled
    // on the client-side before creating invitation-send records. This ensures no duplicate validation
    // and prevents race conditions where server-side validation finds records just created by the client.

    // Get squad member count for additional parameters
    let memberCount = 1
    try {
      const adminDb = await getAdminDb()
      const squadDoc = await adminDb.collection("squads").doc(invitation.squadId).get()
      if (squadDoc.exists) {
        const squadData = squadDoc.data()
        memberCount = squadData?.memberCount || 1
      }
    } catch (error) {
      console.warn("Could not fetch squad member count:", error)
    }

    // Generate the invitation link (with invitation send ID if provided)
    const invitationLink = generateInvitationLink(invitationId, invitationSendId)

    // Prepare additional parameters
    const additionalParams = {
      memberCount,
      invitationDate:
        invitation.createdAt?.toDate?.()?.toLocaleDateString() || new Date().toLocaleDateString(),
    }

    // Send the invitation email with optional template ID and additional parameters
    const result = await sendInvitationEmail(
      invitation,
      invitationLink,
      templateId,
      additionalParams
    )

    // Handle the response
    if (result.success) {
      return NextResponse.json({
        success: true,
        messageId: "messageId" in result ? result.messageId : `email-${Date.now()}`,
      })
    } else {
      console.error("Error sending invitation email:", result.error)
      return NextResponse.json(
        { error: "Failed to send invitation email", details: result.error },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error("Error in invitation email API route:", error)

    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : "Unknown error"
    return NextResponse.json(
      {
        error: "Internal server error",
        details: errorMessage,
      },
      { status: 500 }
    )
  }
}
