import { NextRequest, NextResponse } from "next/server"
import { verifyAuth } from "@/lib/api-auth"
import { sendInvitationEmail, generateInvitationLink } from "@/lib/server/email-service"
import { getAdminDb } from "@/lib/firebase-admin"

interface BatchEmailRequest {
  invitationId: string
  emails: string[]
  templateId?: number
  invitationSendIds: string[] // Pre-created invitation send IDs
}

interface BatchEmailResult {
  email: string
  success: boolean
  error?: string
  sendId: string
}

// Helper function to get invitation data from invitation links only
async function getInvitationData(invitationId: string) {
  try {
    const adminDb = await getAdminDb()

    const invitationLinkDoc = await adminDb.collection("invitation-links").doc(invitationId).get()
    if (invitationLinkDoc.exists) {
      const invitationLinkData = invitationLinkDoc.data()

      // Convert invitation link data to invitation format
      return {
        id: invitationLinkDoc.id,
        squadId: invitationLinkData?.squadId,
        squadName: invitationLinkData?.squadName,
        inviterId: invitationLinkData?.inviterId,
        inviterName: invitationLinkData?.inviterName,
        inviteeId: "", // Not applicable for invitation links
        inviteeEmail: "", // Will be provided by the email being sent to
        status: "pending" as const,
        createdAt: invitationLinkData?.createdAt,
        lastUpdated: null,
        lastResent: null,
      }
    }

    return null
  } catch (error) {
    console.error("Error getting invitation data:", error)
    return null
  }
}

// Helper function to get squad member count (cached for batch processing)
async function getSquadMemberCount(squadId: string): Promise<number> {
  try {
    const adminDb = await getAdminDb()

    const squadDoc = await adminDb.collection("squads").doc(squadId).get()
    if (squadDoc.exists) {
      const squadData = squadDoc.data()
      return squadData?.memberCount || 1
    }
    return 1
  } catch (error) {
    console.warn("Could not fetch squad member count:", error)
    return 1
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = await verifyAuth(request)

    if (!authResult.isAuthenticated) {
      return authResult.response
    }

    // Get the request body
    const body: BatchEmailRequest = await request.json()
    const { invitationId, emails, templateId, invitationSendIds } = body

    if (!invitationId || !emails || !Array.isArray(emails) || emails.length === 0) {
      return NextResponse.json(
        { error: "Missing required fields: invitationId, emails" },
        { status: 400 }
      )
    }

    if (!invitationSendIds || invitationSendIds.length !== emails.length) {
      return NextResponse.json(
        { error: "invitationSendIds must match emails array length" },
        { status: 400 }
      )
    }

    console.log(`Processing batch email request for ${emails.length} emails`)

    // Get the invitation details from invitation links (single call)
    let invitation
    try {
      invitation = await getInvitationData(invitationId)

      if (!invitation) {
        console.log("Invitation not found with ID:", invitationId)
        return NextResponse.json({ error: "Invitation not found" }, { status: 404 })
      }

      console.log("Successfully retrieved invitation for batch processing:", {
        id: invitation.id,
        squadId: invitation.squadId,
        emailCount: emails.length,
      })
    } catch (invitationError) {
      console.error("Error fetching invitation:", invitationError)
      return NextResponse.json(
        {
          error: "Error fetching invitation",
          details: invitationError instanceof Error ? invitationError.message : "Unknown error",
        },
        { status: 500 }
      )
    }

    // Get squad member count once for all emails
    const memberCount = await getSquadMemberCount(invitation.squadId)

    // Prepare additional parameters (shared across all emails)
    const additionalParams = {
      memberCount,
      invitationDate:
        invitation.createdAt?.toDate?.()?.toLocaleDateString() || new Date().toLocaleDateString(),
    }

    // Process emails concurrently with Promise.allSettled for better error handling
    const emailPromises = emails.map(async (email, index): Promise<BatchEmailResult> => {
      const sendId = invitationSendIds[index]

      try {
        // Create invitation object for this specific email
        const emailInvitation = {
          ...invitation,
          inviteeEmail: email,
        }

        // Generate the invitation link with the specific send ID
        const invitationLink = generateInvitationLink(invitationId, sendId)

        // Send the invitation email
        const result = await sendInvitationEmail(
          emailInvitation,
          invitationLink,
          templateId,
          additionalParams
        )

        return {
          email,
          sendId,
          success: result.success,
          error: result.success ? undefined : result.error,
        }
      } catch (error) {
        console.error(`Error sending email to ${email}:`, error)
        return {
          email,
          sendId,
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        }
      }
    })

    // Wait for all emails to be processed concurrently
    const results = await Promise.allSettled(emailPromises)

    // Process results and extract successful/failed emails
    const processedResults: BatchEmailResult[] = []
    let successCount = 0
    let failureCount = 0

    results.forEach((result, index) => {
      if (result.status === "fulfilled") {
        processedResults.push(result.value)
        if (result.value.success) {
          successCount++
        } else {
          failureCount++
        }
      } else {
        // Handle Promise rejection
        processedResults.push({
          email: emails[index],
          sendId: invitationSendIds[index],
          success: false,
          error: result.reason instanceof Error ? result.reason.message : "Promise rejected",
        })
        failureCount++
      }
    })

    console.log(
      `Batch email processing completed: ${successCount} successful, ${failureCount} failed`
    )

    // Return batch results
    return NextResponse.json({
      success: true,
      results: processedResults,
      summary: {
        total: emails.length,
        successful: successCount,
        failed: failureCount,
      },
    })
  } catch (error) {
    console.error("Error in batch invitation email API route:", error)

    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : "Unknown error"
    return NextResponse.json(
      {
        error: "Internal server error",
        details: errorMessage,
      },
      { status: 500 }
    )
  }
}
