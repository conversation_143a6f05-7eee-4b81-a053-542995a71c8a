import { type NextRequest, NextResponse } from "next/server"
import { getAdminAuth } from "@/lib/firebase-admin"

export async function POST(request: NextRequest) {
  try {
    const { idToken } = await request.json()

    if (!idToken) {
      return NextResponse.json({ error: "Missing Firebase ID token" }, { status: 400 })
    }

    // Get Firebase Admin Auth instance
    const adminAuth = await getAdminAuth()

    try {
      // Verify the token first
      const decodedToken = await adminAuth.verifyIdToken(idToken)

      if (!decodedToken.uid) {
        return NextResponse.json({ error: "Invalid token: missing user ID" }, { status: 401 })
      }

      // Create a session cookie using Firebase Admin
      // Session cookie expires in 7 days (same as client-side implementation)
      const expiresIn = 60 * 60 * 24 * 7 * 1000 // 7 days in milliseconds
      const sessionCookie = await adminAuth.createSessionCookie(idToken, { expiresIn })

      // Create response with success
      const response = NextResponse.json({
        success: true,
        user: {
          uid: decodedToken.uid,
          email: decodedToken.email,
          emailVerified: decodedToken.email_verified,
          provider: decodedToken.firebase?.sign_in_provider,
        },
      })

      // Set the session cookie
      const maxAge = 60 * 60 * 24 * 7 // 7 days in seconds
      const cookieOptions = [
        `firebase-auth-token=${sessionCookie}`,
        `Max-Age=${maxAge}`,
        "Path=/",
        "SameSite=Lax",
        "HttpOnly", // Make it HttpOnly for security
      ]

      // Add Secure flag in production
      if (process.env.NODE_ENV === "production") {
        cookieOptions.push("Secure")
      }

      response.headers.set("Set-Cookie", cookieOptions.join("; "))

      return response
    } catch (verificationError: any) {
      console.error("Token verification failed:", verificationError)

      // Handle specific Firebase Auth errors
      if (verificationError.code === "auth/id-token-expired") {
        return NextResponse.json({ error: "Token expired. Please sign in again." }, { status: 401 })
      }

      if (verificationError.code === "auth/id-token-revoked") {
        return NextResponse.json({ error: "Token revoked. Please sign in again." }, { status: 401 })
      }

      return NextResponse.json({ error: "Invalid or expired token" }, { status: 401 })
    }
  } catch (error: any) {
    console.error("Login API error:", error)
    return NextResponse.json(
      {
        error: error.message || "An error occurred during login",
      },
      { status: 500 }
    )
  }
}
