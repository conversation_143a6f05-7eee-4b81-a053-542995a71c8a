import { NextRequest, NextResponse } from "next/server"
import { FlatSubscriptionAdminService } from "@/lib/domains/user-subscription/flat-subscription-admin.service"

/**
 * Admin endpoint for manual subscription recovery
 *
 * Use this when webhook processing fails and you need to manually recover a user's subscription
 *
 * POST /api/admin/subscription-recovery
 * Body: { userId: string, stripeSubscriptionId?: string }
 * Headers: { Authorization: Bearer <ADMIN_API_KEY> }
 */
export async function POST(request: NextRequest) {
  try {
    // Verify admin authorization
    const authHeader = request.headers.get("authorization")
    const expectedAuth = `Bearer ${process.env.ADMIN_API_KEY}`

    if (!authHeader || authHeader !== expectedAuth) {
      console.error("Unauthorized admin request")
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { userId, stripeSubscriptionId } = body

    if (!userId) {
      return NextResponse.json({ error: "userId is required" }, { status: 400 })
    }

    console.log(`🔧 Admin subscription recovery requested for user: ${userId}`)
    if (stripeSubscriptionId) {
      console.log(`   Targeting subscription: ${stripeSubscriptionId}`)
    }

    const result = await FlatSubscriptionAdminService.recoverUserSubscription(
      userId,
      stripeSubscriptionId
    )

    if (result.success) {
      console.log(`✅ Recovery completed: ${result.data?.message}`)

      return NextResponse.json({
        success: true,
        message: "Subscription recovery completed",
        data: result.data,
      })
    } else {
      console.error(`❌ Recovery failed: ${result.error?.message}`)

      return NextResponse.json(
        {
          success: false,
          error: "Subscription recovery failed",
          message: result.error?.message,
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error("💥 Subscription recovery exception:", error)

    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}

/**
 * GET endpoint to check a user's current subscription status
 */
export async function GET(request: NextRequest) {
  try {
    // Verify admin authorization
    const authHeader = request.headers.get("authorization")
    const expectedAuth = `Bearer ${process.env.ADMIN_API_KEY}`

    if (!authHeader || authHeader !== expectedAuth) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get("userId")

    if (!userId) {
      return NextResponse.json({ error: "userId parameter is required" }, { status: 400 })
    }

    console.log(`📊 Checking subscription status for user: ${userId}`)

    const subscriptions = await FlatSubscriptionAdminService.getUserSubscriptions(userId)
    const currentSubscription = await FlatSubscriptionAdminService.getCurrentSubscription(userId)

    return NextResponse.json({
      success: true,
      data: {
        userId,
        currentSubscription,
        allSubscriptions: subscriptions,
        subscriptionCount: subscriptions.length,
        stripeSubscriptions: subscriptions.filter((sub) => sub.source === "stripe"),
        appliedSubscriptions: subscriptions.filter((sub) => sub.status === "applied"),
      },
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: "Failed to check subscription status",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}
