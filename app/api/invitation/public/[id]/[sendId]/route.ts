import { NextRequest, NextResponse } from "next/server"
import { getAdminDb } from "@/lib/firebase-admin"

/**
 * Public API route to fetch specific invitation send details for redirect logic
 * This route does not require authentication but only returns essential data
 * for determining the appropriate redirect flow for specific email invitations
 *
 * URL format: /api/invitation/public/{invitationId}/{invitationSendId}
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string; sendId: string }> }
) {
  try {
    const { id: invitationId, sendId: invitationSendId } = await params

    if (!invitationId || !invitationSendId) {
      return NextResponse.json({ error: "Invitation ID and Send ID are required" }, { status: 400 })
    }

    // Get invitation link using Admin SDK
    const adminDb = await getAdminDb()

    const invitationLinkDoc = await adminDb.collection("invitation-links").doc(invitationId).get()

    if (!invitationLinkDoc.exists) {
      return NextResponse.json({ error: "Invitation not found" }, { status: 404 })
    }

    const invitationLinkData = invitationLinkDoc.data()

    // Check if the invitation is expired or inactive
    const now = new Date()
    const expiresAt = invitationLinkData?.expiresAt?.toDate()

    if (!invitationLinkData?.isActive || (expiresAt && expiresAt <= now)) {
      return NextResponse.json(
        { error: "Invitation has expired or is no longer active" },
        { status: 410 }
      )
    }

    // Get the specific invitation send
    const squadId = invitationLinkData?.squadId
    if (!squadId) {
      return NextResponse.json({ error: "Invalid invitation data" }, { status: 400 })
    }

    const invitationSendDoc = await adminDb
      .collection(`squads/${squadId}/invitation-sends`)
      .doc(invitationSendId)
      .get()

    if (!invitationSendDoc.exists) {
      return NextResponse.json({ error: "Invitation send not found" }, { status: 404 })
    }

    const invitationSendData = invitationSendDoc.data()

    // Verify that this invitation send belongs to the invitation
    if (invitationSendData?.invitationId !== invitationId) {
      return NextResponse.json({ error: "Invalid invitation send" }, { status: 400 })
    }

    // Return specific invitation send data
    return NextResponse.json({
      id: invitationId,
      sendId: invitationSendId,
      type: "invitation_send",
      squadId: squadId,
      squadName: invitationLinkData?.squadName,
      inviterName: invitationLinkData?.inviterName,
      email: invitationSendData?.email,
      status: invitationSendData?.status,
      sentAt: invitationSendData?.sentAt,
    })
  } catch (error) {
    console.error("Error fetching specific invitation send:", error)
    return NextResponse.json(
      {
        error: "Failed to fetch invitation details",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}
