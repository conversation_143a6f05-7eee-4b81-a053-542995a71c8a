import { NextRequest, NextResponse } from "next/server"
import { getAdminDb } from "@/lib/firebase-admin"

/**
 * Public API route to fetch minimal invitation details for redirect logic
 * This route does not require authentication but only returns essential data
 * for determining the appropriate redirect flow
 *
 * Note: Only supports invitation-links, legacy invitations are no longer supported
 * in public routes for security reasons
 */
export async function GET(_request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: invitationId } = await params

    if (!invitationId) {
      return NextResponse.json({ error: "Invitation ID is required" }, { status: 400 })
    }

    // Get invitation link using Admin SDK
    const adminDb = await getAdminDb()

    const invitationLinkDoc = await adminDb.collection("invitation-links").doc(invitationId).get()

    if (!invitationLinkDoc.exists) {
      return NextResponse.json({ error: "Invitation not found" }, { status: 404 })
    }

    const invitationLinkData = invitationLinkDoc.data()

    // Check if the invitation is expired or inactive
    const now = new Date()
    const expiresAt = invitationLinkData?.expiresAt?.toDate()

    if (!invitationLinkData?.isActive || (expiresAt && expiresAt <= now)) {
      return NextResponse.json(
        { error: "Invitation has expired or is no longer active" },
        { status: 410 }
      )
    }

    // Get invitation-sends to check if there are specific invitees
    const squadId = invitationLinkData?.squadId
    if (!squadId) {
      return NextResponse.json({ error: "Invalid invitation data" }, { status: 400 })
    }

    // Get invitation sends for this invitation to check for specific emails
    const invitationSendsSnapshot = await adminDb
      .collection(`squads/${squadId}/invitation-sends`)
      .where("invitationId", "==", invitationId)
      .get()

    const invitationSends = invitationSendsSnapshot.docs.map((doc: any) => doc.data())

    // Return minimal data for invitation link
    return NextResponse.json({
      id: invitationLinkDoc.id,
      type: "invitation_link",
      squadId: squadId,
      squadName: invitationLinkData?.squadName,
      inviterName: invitationLinkData?.inviterName,
      hasSpecificInvitees: invitationSends.length > 0,
      inviteeEmails: invitationSends.map((send: any) => send.email), // For user existence checking
    })
  } catch (error) {
    console.error("Error fetching public invitation:", error)
    return NextResponse.json(
      {
        error: "Failed to fetch invitation details",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}
