import { NextRequest, NextResponse } from "next/server"
import { getAdminDb } from "@/lib/firebase-admin"
import { sendEmail } from "@/lib/server/email-service"
import { EmailTemplates } from "@/lib/server/email-templates"
import { getTodayUTC } from "@/lib/utils/date-utils"

// Rate limiting and origin validation for security
const ALLOWED_ORIGINS = [
  "https://vercel.com",
  "https://cron-job.vercel.app",
  process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : null,
].filter(Boolean)

const CRON_SECRET = process.env.CRON_SECRET || "default-secret"

/**
 * Create a notification using the admin SDK
 * This bypasses the client-side authentication requirements
 */
async function createAdminNotification(
  adminDb: any,
  userId: string,
  notificationData: {
    userId: string
    type: string
    title: string
    message: string
    read: boolean
    actionUrl?: string
    relatedEntityId?: string
    relatedEntityType?: string
  }
): Promise<string> {
  try {
    const notificationRef = adminDb
      .collection("users")
      .doc(userId)
      .collection("notifications")
      .doc()
    const notificationId = notificationRef.id

    await notificationRef.set({
      ...notificationData,
      id: notificationId,
      createdAt: new Date(), // Use regular Date instead of serverTimestamp for admin SDK
    })

    return notificationId
  } catch (error) {
    console.error("Error creating admin notification:", error)
    throw error
  }
}

/**
 * Validate cron job request for security
 */
function validateCronRequest(request: NextRequest): { isValid: boolean; error?: string } {
  // Validate cron secret for security
  const authHeader = request.headers.get("authorization")
  const providedSecret = authHeader?.replace("Bearer ", "")

  if (providedSecret !== CRON_SECRET) {
    console.error("Unauthorized cron job access attempt")
    return { isValid: false, error: "Unauthorized" }
  }

  // Validate origin for additional security
  const origin = request.headers.get("origin")
  const userAgent = request.headers.get("user-agent")

  // Allow Vercel cron jobs (they don't always have origin headers)
  const isVercelCron = userAgent?.includes("vercel") || !origin
  const isAllowedOrigin = origin && ALLOWED_ORIGINS.includes(origin)

  if (!isVercelCron && !isAllowedOrigin) {
    console.error("Invalid origin for cron job:", origin)
    return { isValid: false, error: "Invalid origin" }
  }

  return { isValid: true }
}

/**
 * Activate trips that should become active (planning → active)
 * This function is idempotent and safe to run multiple times
 */
async function activateTrips() {
  console.log("Starting trip activation process...")

  // Get Firebase Admin instance
  const adminDb = await getAdminDb()

  // Get current date in UTC (normalized to midnight)
  const now = new Date()
  const currentDateUTC = getTodayUTC()

  const tripsRef = adminDb.collection("trips")
  const activatedTrips: any[] = []
  const batch = adminDb.batch()

  // Check for trips that should become active (planning → active)
  console.log("Checking for trips to activate...")
  const planningTripsQuery = tripsRef.where("status", "==", "planning")
  const planningTripsSnapshot = await planningTripsQuery.get()

  for (const tripDoc of planningTripsSnapshot.docs) {
    const tripData = tripDoc.data()
    const tripId = tripDoc.id

    // Convert Firestore timestamp to Date
    const startDate = tripData.startDate?.toDate()
    if (!startDate) continue

    // Since dates are now stored as UTC midnight, we can compare directly
    // Check if trip should be activated (startDate <= current date)
    console.log("Trip start date (UTC):", startDate.toISOString())
    console.log("Current date (UTC):", currentDateUTC.toISOString())
    console.log(startDate <= currentDateUTC)

    if (startDate <= currentDateUTC) {
      console.log(`Activating trip ${tripId} (started: ${startDate.toISOString()})`)

      // Update trip status to active
      const tripRef = adminDb.collection("trips").doc(tripId)
      batch.update(tripRef, {
        status: "active",
        updatedAt: now,
      })

      // Get trip attendees for notifications
      const userTripsRef = adminDb.collection("userTrips")
      const userTripsQuery = userTripsRef
        .where("tripId", "==", tripId)
        .where("status", "==", "going")
      const userTripsSnapshot = await userTripsQuery.get()

      const attendeeIds = userTripsSnapshot.docs.map((doc: any) => doc.data().userId)

      activatedTrips.push({
        tripId,
        tripName: tripData.name || "Unknown Trip",
        destination: tripData.destination || "Unknown Destination",
        startDate: tripData.startDate?.toDate(),
        endDate: tripData.endDate?.toDate(),
        squadId: tripData.squadId,
        attendeeIds,
      })
    }
  }

  // Send notifications and emails for activated trips BEFORE committing trip status changes
  // This ensures we don't mark trips as active if notifications fail
  const activationNotificationResults: any[] = []
  const activationEmailResults: any[] = []
  const successfullyNotifiedTrips: string[] = []

  for (const trip of activatedTrips) {
    try {
      let notificationsSent = 0

      // Create notifications for all trip members about trip activation
      for (const userId of trip.attendeeIds) {
        try {
          await createAdminNotification(adminDb, userId, {
            userId,
            type: "trip_update",
            title: "Trip Started! 🚀",
            message: `Your trip to ${trip.destination} has started! Have an amazing time and stay safe.`,
            read: false,
            actionUrl: `/trips/${trip.tripId}?callback=${encodeURIComponent(`/trips/${trip.tripId}`)}`,
            relatedEntityId: trip.tripId,
            relatedEntityType: "trip",
          })
          notificationsSent++
        } catch (notifError) {
          console.error(`Error creating activation notification for user ${userId}:`, notifError)
          // Don't throw here - we want to try all users
        }
      }

      // Only proceed with emails if at least some notifications were sent
      // or if there are no attendees to notify
      if (notificationsSent > 0 || trip.attendeeIds.length === 0) {
        successfullyNotifiedTrips.push(trip.tripId)
        activationNotificationResults.push({
          tripId: trip.tripId,
          notificationsSent,
        })

        // Get squad information for email
        let squadName = "Unknown Squad"
        try {
          const squadDoc = await adminDb.collection("squads").doc(trip.squadId).get()
          if (squadDoc.exists) {
            squadName = squadDoc.data()?.name || "Unknown Squad"
          }
        } catch (squadError) {
          console.error(`Error getting squad info for trip ${trip.tripId}:`, squadError)
        }

        // Get user details for email sending
        const userDocs = await Promise.all(
          trip.attendeeIds.map((userId: string) => adminDb.collection("users").doc(userId).get())
        )

        const users = userDocs
          .filter((doc) => doc.exists)
          .map((doc) => ({ id: doc.id, ...doc.data() }))

        // Send emails to all trip members
        for (const user of users) {
          try {
            const emailResult = await sendEmail({
              to: user.email,
              templateId: EmailTemplates.TRIP_STARTED,
              params: {
                tripName: trip.tripName,
                squadName: squadName,
                destination: trip.destination,
                startDate: trip.startDate?.toLocaleDateString() || "Unknown",
                endDate: trip.endDate?.toLocaleDateString() || "Unknown",
                tripLink: `${process.env.NEXT_PUBLIC_APP_URL}/trips/${trip.tripId}?callback=${encodeURIComponent(`/trips/${trip.tripId}`)}`,
              },
            })

            if (emailResult.success) {
              activationEmailResults.push({
                userId: user.id,
                email: user.email,
                messageId: emailResult.messageId,
              })
            } else {
              console.error(`Failed to send email to ${user.email}:`, emailResult.error)
            }
          } catch (emailError) {
            console.error(`Error sending email to ${user.email}:`, emailError)
          }
        }
      } else {
        console.error(
          `Failed to send any notifications for trip ${trip.tripId}, skipping trip activation`
        )
      }
    } catch (error) {
      console.error(`Error processing notifications/emails for trip ${trip.tripId}:`, error)
    }
  }

  // Only commit trip status updates for trips where notifications were successful
  const finalBatch = adminDb.batch()
  let tripsActuallyActivated = 0

  for (const trip of activatedTrips) {
    if (successfullyNotifiedTrips.includes(trip.tripId)) {
      const tripRef = adminDb.collection("trips").doc(trip.tripId)
      finalBatch.update(tripRef, {
        status: "active",
        updatedAt: now,
      })
      tripsActuallyActivated++
    }
  }

  // Commit the batch update (idempotent - safe to run multiple times)
  if (tripsActuallyActivated > 0) {
    await finalBatch.commit()
    console.log(`Updated ${tripsActuallyActivated} trips to active status`)
  }

  return {
    tripsProcessed: planningTripsSnapshot.size,
    tripsEligibleForActivation: activatedTrips.length,
    tripsActuallyActivated,
    activatedTrips: activatedTrips
      .filter((t) => successfullyNotifiedTrips.includes(t.tripId))
      .map((t) => ({
        tripId: t.tripId,
        destination: t.destination,
        attendeeCount: t.attendeeIds.length,
      })),
    activationNotificationResults,
    activationEmailResults,
  }
}

export async function GET(request: NextRequest) {
  try {
    const validation = validateCronRequest(request)
    if (!validation.isValid) {
      return NextResponse.json(
        { error: validation.error },
        { status: validation.error === "Unauthorized" ? 401 : 403 }
      )
    }

    const result = await activateTrips()

    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      ...result,
    }

    console.log("Trip activation completed:", response)
    return NextResponse.json(response)
  } catch (error) {
    console.error("Error in trip activation cron job:", error)
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}

// Also handle POST requests for manual testing
export async function POST(request: NextRequest) {
  return GET(request)
}
