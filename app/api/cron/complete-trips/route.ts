import { NextRequest, NextResponse } from "next/server"
import { getAdminDb } from "@/lib/firebase-admin"
import { sendEmail } from "@/lib/server/email-service"
import { EmailTemplates } from "@/lib/server/email-templates"
import { getTodayUTC } from "@/lib/utils/date-utils"

// Rate limiting and origin validation for security
const ALLOWED_ORIGINS = [
  "https://vercel.com",
  "https://cron-job.vercel.app",
  process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : null,
].filter(Boolean)

const CRON_SECRET = process.env.CRON_SECRET || "default-secret"

/**
 * Create a notification using the admin SDK
 * This bypasses the client-side authentication requirements
 */
async function createAdminNotification(
  adminDb: any,
  userId: string,
  notificationData: {
    userId: string
    type: string
    title: string
    message: string
    read: boolean
    actionUrl?: string
    relatedEntityId?: string
    relatedEntityType?: string
  }
): Promise<string> {
  try {
    const notificationRef = adminDb
      .collection("users")
      .doc(userId)
      .collection("notifications")
      .doc()
    const notificationId = notificationRef.id

    await notificationRef.set({
      ...notificationData,
      id: notificationId,
      createdAt: new Date(), // Use regular Date instead of serverTimestamp for admin SDK
    })

    return notificationId
  } catch (error) {
    console.error("Error creating admin notification:", error)
    throw error
  }
}

/**
 * Validate cron job request for security
 */
function validateCronRequest(request: NextRequest): { isValid: boolean; error?: string } {
  // Validate cron secret for security
  const authHeader = request.headers.get("authorization")
  const providedSecret = authHeader?.replace("Bearer ", "")

  if (providedSecret !== CRON_SECRET) {
    console.error("Unauthorized cron job access attempt")
    return { isValid: false, error: "Unauthorized" }
  }

  // Validate origin for additional security
  const origin = request.headers.get("origin")
  const userAgent = request.headers.get("user-agent")

  // Allow Vercel cron jobs (they don't always have origin headers)
  const isVercelCron = userAgent?.includes("vercel") || !origin
  const isAllowedOrigin = origin && ALLOWED_ORIGINS.includes(origin)

  if (!isVercelCron && !isAllowedOrigin) {
    console.error("Invalid origin for cron job:", origin)
    return { isValid: false, error: "Invalid origin" }
  }

  return { isValid: true }
}

/**
 * Complete trips that should be marked as completed (active → completed)
 * This function is idempotent and safe to run multiple times
 */
async function completeTrips() {
  console.log("Starting trip completion process...")

  // Get Firebase Admin instance
  const adminDb = await getAdminDb()

  // Get current date in UTC (normalized to midnight)
  const now = new Date()
  const currentDateUTC = getTodayUTC()

  const tripsRef = adminDb.collection("trips")
  const completedTrips: any[] = []
  const batch = adminDb.batch()

  // Check for trips that should be completed (active → completed)
  console.log("Checking for trips to complete...")
  const activeTripsQuery = tripsRef.where("status", "==", "active")
  const activeTripsSnapshot = await activeTripsQuery.get()

  // Process each active trip
  for (const tripDoc of activeTripsSnapshot.docs) {
    const tripData = tripDoc.data()
    const tripId = tripDoc.id

    // Convert Firestore timestamp to Date
    const endDate = tripData.endDate?.toDate()
    if (!endDate) continue

    // Since dates are now stored as UTC midnight, we can compare directly
    // Check if trip should be completed (endDate < current date)
    console.log("Trip end date (UTC):", endDate.toISOString())
    console.log("Current date (UTC):", currentDateUTC.toISOString())

    if (endDate < currentDateUTC) {
      console.log(`Marking trip ${tripId} as completed (ended: ${endDate.toISOString()})`)

      // Update trip status to completed
      const tripRef = adminDb.collection("trips").doc(tripId)
      batch.update(tripRef, {
        status: "completed",
        updatedAt: now,
      })

      // Get trip attendees for notifications
      const userTripsRef = adminDb.collection("userTrips")
      const userTripsQuery = userTripsRef
        .where("tripId", "==", tripId)
        .where("status", "==", "going")
      const userTripsSnapshot = await userTripsQuery.get()

      const attendeeIds = userTripsSnapshot.docs.map((doc: any) => doc.data().userId)

      completedTrips.push({
        tripId,
        tripName: tripData.name || "Unknown Trip",
        destination: tripData.destination || "Unknown Destination",
        endDate: tripData.endDate?.toDate(),
        squadId: tripData.squadId,
        attendeeIds,
      })
    }
  }

  // Send notifications and emails for completed trips BEFORE committing trip status changes
  // This ensures we don't mark trips as completed if notifications fail
  const completionNotificationResults: any[] = []
  const completionEmailResults: any[] = []
  const successfullyNotifiedTrips: string[] = []

  for (const trip of completedTrips) {
    try {
      let notificationsSent = 0

      // Create notifications for all trip members
      for (const userId of trip.attendeeIds) {
        try {
          await createAdminNotification(adminDb, userId, {
            userId,
            type: "trip_completed",
            title: "Trip Completed! 🎉",
            message: `Your trip to ${trip.destination} has been completed. Share your experience by reviewing the trip!`,
            read: false,
            actionUrl: `/trips/${trip.tripId}?callback=${encodeURIComponent(`/trips/${trip.tripId}`)}`,
            relatedEntityId: trip.tripId,
            relatedEntityType: "trip",
          })
          notificationsSent++
        } catch (notifError) {
          console.error(`Error creating notification for user ${userId}:`, notifError)
          // Don't throw here - we want to try all users
        }
      }

      // Only proceed with emails if at least some notifications were sent
      // or if there are no attendees to notify
      if (notificationsSent > 0 || trip.attendeeIds.length === 0) {
        successfullyNotifiedTrips.push(trip.tripId)
        completionNotificationResults.push({
          tripId: trip.tripId,
          notificationsSent,
        })

        // Get squad information for email
        let squadName = "Unknown Squad"
        try {
          const squadDoc = await adminDb.collection("squads").doc(trip.squadId).get()
          if (squadDoc.exists) {
            squadName = squadDoc.data()?.name || "Unknown Squad"
          }
        } catch (squadError) {
          console.error(`Error getting squad info for trip ${trip.tripId}:`, squadError)
        }

        // Get user details for email sending
        const userDocs = await Promise.all(
          trip.attendeeIds.map((userId: string) => adminDb.collection("users").doc(userId).get())
        )

        const users = userDocs
          .filter((doc) => doc.exists)
          .map((doc) => ({ id: doc.id, ...doc.data() }))

        // Send emails to all trip members
        for (const user of users) {
          try {
            const emailResult = await sendEmail({
              to: user.email,
              templateId: EmailTemplates.TRIP_COMPLETED,
              params: {
                tripName: trip.tripName,
                squadName: squadName,
                destination: trip.destination,
                completionDate: trip.endDate?.toLocaleDateString() || "Unknown",
                reviewLink: `${process.env.NEXT_PUBLIC_APP_URL}/trips/${trip.tripId}?callback=${encodeURIComponent(`/trips/${trip.tripId}`)}`,
              },
            })

            if (emailResult.success) {
              completionEmailResults.push({
                userId: user.id,
                email: user.email,
                messageId: emailResult.messageId,
              })
            } else {
              console.error(`Failed to send email to ${user.email}:`, emailResult.error)
            }
          } catch (emailError) {
            console.error(`Error sending email to ${user.email}:`, emailError)
          }
        }
      } else {
        console.error(
          `Failed to send any notifications for trip ${trip.tripId}, skipping trip completion`
        )
      }
    } catch (error) {
      console.error(`Error processing notifications/emails for trip ${trip.tripId}:`, error)
    }
  }

  // Only commit trip status updates for trips where notifications were successful
  const finalBatch = adminDb.batch()
  let tripsActuallyCompleted = 0

  for (const trip of completedTrips) {
    if (successfullyNotifiedTrips.includes(trip.tripId)) {
      const tripRef = adminDb.collection("trips").doc(trip.tripId)
      finalBatch.update(tripRef, {
        status: "completed",
        updatedAt: now,
      })
      tripsActuallyCompleted++
    }
  }

  // Commit the batch update (idempotent - safe to run multiple times)
  if (tripsActuallyCompleted > 0) {
    await finalBatch.commit()
    console.log(`Updated ${tripsActuallyCompleted} trips to completed status`)
  }

  return {
    tripsProcessed: activeTripsSnapshot.size,
    tripsEligibleForCompletion: completedTrips.length,
    tripsActuallyCompleted,
    completedTrips: completedTrips
      .filter((t) => successfullyNotifiedTrips.includes(t.tripId))
      .map((t) => ({
        tripId: t.tripId,
        destination: t.destination,
        attendeeCount: t.attendeeIds.length,
      })),
    completionNotificationResults,
    completionEmailResults,
  }
}

export async function GET(request: NextRequest) {
  try {
    const validation = validateCronRequest(request)
    if (!validation.isValid) {
      return NextResponse.json(
        { error: validation.error },
        { status: validation.error === "Unauthorized" ? 401 : 403 }
      )
    }

    const result = await completeTrips()

    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      ...result,
    }

    console.log("Trip completion completed:", response)
    return NextResponse.json(response)
  } catch (error) {
    console.error("Error in trip completion cron job:", error)
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}

// Also handle POST requests for manual testing
export async function POST(request: NextRequest) {
  return GET(request)
}
