import { NextRequest, NextResponse } from "next/server"
import { createCustomerPortalSession } from "@/lib/server/stripe"
import { getAdminAuth, getAdminDb } from "@/lib/firebase-admin"

export async function POST(request: NextRequest) {
  try {
    // Verify the user is authenticated
    const authHeader = request.headers.get("Authorization")
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const token = authHeader.split("Bearer ")[1]

    // Initialize Firebase Admin
    const adminAuth = await getAdminAuth()

    let decodedToken
    try {
      decodedToken = await adminAuth.verifyIdToken(token)
    } catch (error) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 })
    }

    const userId = decodedToken.uid

    // Get the user's subscription from Firestore using Admin SDK to get their Stripe customer ID
    const adminDb = await getAdminDb()

    // Query the userSubscriptions collection for Stripe subscription entries
    console.log(`Getting Stripe subscription entries for userId: ${userId}`)
    const stripeSubscriptionsQuery = await adminDb
      .collection("userSubscriptions")
      .where("userId", "==", userId)
      .where("source", "==", "stripe")
      .get()

    let stripeCustomerId = null

    if (!stripeSubscriptionsQuery.empty) {
      // Get the first Stripe subscription entry (there should only be one per user)
      const stripeSubscriptionDoc = stripeSubscriptionsQuery.docs[0]
      const stripeSubscriptionData = stripeSubscriptionDoc.data()
      console.log(`Stripe subscription entry found for userId: ${userId}`)
      console.log(`Stripe subscription data:`, JSON.stringify(stripeSubscriptionData, null, 2))

      // Extract customer ID from the subscription data
      if (stripeSubscriptionData?.subscriptionData?.customerId) {
        stripeCustomerId = stripeSubscriptionData.subscriptionData.customerId
      }
    }

    // Check if we found a Stripe customer ID
    if (!stripeCustomerId) {
      console.error(`User does not have a Stripe customer ID: ${userId}`)
      return NextResponse.json(
        {
          error: "User does not have a Stripe customer ID",
          message:
            "You need a Stripe subscription to access the customer portal. Only Stripe subscriptions can be managed through this portal.",
        },
        { status: 400 }
      )
    }
    console.log(`User has Stripe customer ID: ${stripeCustomerId}`)

    // Create a customer portal session
    const portalSession = await createCustomerPortalSession(stripeCustomerId)

    return NextResponse.json({ url: portalSession.url })
  } catch (error) {
    console.error("Error creating customer portal session:", error)
    return NextResponse.json({ error: "Failed to create customer portal session" }, { status: 500 })
  }
}
