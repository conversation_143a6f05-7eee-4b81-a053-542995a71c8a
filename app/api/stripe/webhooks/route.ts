import { NextRequest, NextResponse } from "next/server"
import { stripe } from "@/lib/server/stripe"
import { getAdminDb, getAdminFieldValue } from "@/lib/firebase-admin"
import Stripe from "stripe"
import { FlatSubscriptionWebhooks } from "@/lib/domains/user-subscription/flat-subscription.webhooks"

/**
 * Handle experience booking webhook events
 */
async function handleExperienceBookingCheckoutCompleted(session: Stripe.Checkout.Session) {
  try {
    const { experienceId, bookingId, userId, type } = session.metadata || {}

    if (type !== "experience_booking" || !experienceId || !bookingId || !userId) {
      console.log("Not an experience booking checkout session")
      return
    }

    console.log(`Processing experience booking checkout completion: ${bookingId}`)

    // Get admin Firestore instance
    const adminDb = await getAdminDb()
    const adminFieldValue = await getAdminFieldValue()
    if (!adminDb) {
      console.error("Admin Firestore not available")
      return
    }

    // Verify the booking exists using admin access
    const bookingRef = adminDb
      .collection("localExperiences")
      .doc(experienceId)
      .collection("bookings")
      .doc(bookingId)

    const bookingDoc = await bookingRef.get()
    if (!bookingDoc.exists) {
      console.error(`Experience booking not found: ${experienceId}/${bookingId}`)
      return
    }

    const booking = bookingDoc.data()

    // Only process if booking is still pending
    if (booking?.status !== "pending") {
      console.log(
        `Experience booking ${bookingId} is already processed (status: ${booking?.status})`
      )
      return
    }

    // Confirm the booking using admin access
    const updateData = {
      status: "confirmed",
      paymentStatus: "paid",
      confirmedAt: adminFieldValue.serverTimestamp(),
      stripeSessionId: session.id,
      stripePaymentIntentId: session.payment_intent as string,
      stripeCustomerId: session.customer as string,
    }

    // Update both booking collections atomically
    const batch = adminDb.batch()

    // Update experience booking
    batch.update(bookingRef, updateData)

    // Update user booking copy
    const userBookingRef = adminDb
      .collection("users")
      .doc(userId)
      .collection("localExperienceBookings")
      .doc(bookingId)
    batch.update(userBookingRef, updateData)

    await batch.commit()

    console.log(`Successfully confirmed experience booking: ${bookingId}`)
  } catch (error) {
    console.error("Error handling experience booking checkout completed:", error)
  }
}

async function handleExperienceBookingPaymentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  try {
    // Get the checkout session associated with this payment intent
    const sessions = await stripe.checkout.sessions.list({
      payment_intent: paymentIntent.id,
      limit: 1,
    })

    if (sessions.data.length === 0) {
      console.log("No checkout session found for payment intent:", paymentIntent.id)
      return
    }

    const session = sessions.data[0]
    const { experienceId, bookingId, userId, type } = session.metadata || {}

    if (type !== "experience_booking" || !experienceId || !bookingId || !userId) {
      console.log("Not an experience booking payment intent")
      return
    }

    // Get admin Firestore instance
    const adminDb = await getAdminDb()
    if (!adminDb) {
      console.error("Admin Firestore not available")
      return
    }

    // Update booking payment status using admin access
    const updateData = {
      paymentStatus: "paid",
      stripePaymentIntentId: paymentIntent.id,
    }

    // Update both booking collections atomically
    const batch = adminDb.batch()

    // Update experience booking
    const bookingRef = adminDb
      .collection("localExperiences")
      .doc(experienceId)
      .collection("bookings")
      .doc(bookingId)
    batch.update(bookingRef, updateData)

    // Update user booking copy
    const userBookingRef = adminDb
      .collection("users")
      .doc(userId)
      .collection("localExperienceBookings")
      .doc(bookingId)
    batch.update(userBookingRef, updateData)

    await batch.commit()

    console.log(`Experience booking payment confirmed: ${bookingId}`)
  } catch (error) {
    console.error("Error handling experience booking payment succeeded:", error)
  }
}

async function handleExperienceBookingPaymentFailed(paymentIntent: Stripe.PaymentIntent) {
  try {
    // Get the checkout session associated with this payment intent
    const sessions = await stripe.checkout.sessions.list({
      payment_intent: paymentIntent.id,
      limit: 1,
    })

    if (sessions.data.length === 0) {
      console.log("No checkout session found for failed payment intent:", paymentIntent.id)
      return
    }

    const session = sessions.data[0]
    const { experienceId, bookingId, userId, type } = session.metadata || {}

    if (type !== "experience_booking" || !experienceId || !bookingId || !userId) {
      console.log("Not an experience booking payment intent")
      return
    }

    // Get admin Firestore instance
    const adminDb = await getAdminDb()
    if (!adminDb) {
      console.error("Admin Firestore not available")
      return
    }

    // Update booking payment status using admin access
    const updateData = {
      paymentStatus: "failed",
      stripePaymentIntentId: paymentIntent.id,
    }

    // Update both booking collections atomically
    const batch = adminDb.batch()

    // Update experience booking
    const bookingRef = adminDb
      .collection("localExperiences")
      .doc(experienceId)
      .collection("bookings")
      .doc(bookingId)
    batch.update(bookingRef, updateData)

    // Update user booking copy
    const userBookingRef = adminDb
      .collection("users")
      .doc(userId)
      .collection("localExperienceBookings")
      .doc(bookingId)
    batch.update(userBookingRef, updateData)

    await batch.commit()

    console.log(`Experience booking payment failed: ${bookingId}`)
  } catch (error) {
    console.error("Error handling experience booking payment failed:", error)
  }
}

async function handleExperienceBookingSessionExpired(session: Stripe.Checkout.Session) {
  try {
    const { experienceId, bookingId, userId, type } = session.metadata || {}

    if (type !== "experience_booking" || !experienceId || !bookingId || !userId) {
      console.log("Not an experience booking session")
      return
    }

    // Get admin Firestore instance
    const adminDb = await getAdminDb()
    const adminFieldValue = await getAdminFieldValue()
    if (!adminDb) {
      console.error("Admin Firestore not available")
      return
    }

    // Cancel the booking due to expired session using admin access
    const updateData = {
      status: "cancelled",
      cancelledAt: adminFieldValue.serverTimestamp(),
      cancellationReason: "Checkout session expired",
    }

    // Update both booking collections atomically
    const batch = adminDb.batch()

    // Update experience booking
    const bookingRef = adminDb
      .collection("localExperiences")
      .doc(experienceId)
      .collection("bookings")
      .doc(bookingId)
    batch.update(bookingRef, updateData)

    // Update user booking copy
    const userBookingRef = adminDb
      .collection("users")
      .doc(userId)
      .collection("localExperienceBookings")
      .doc(bookingId)
    batch.update(userBookingRef, updateData)

    await batch.commit()

    console.log(`Cancelled experience booking due to expired session: ${bookingId}`)
  } catch (error) {
    console.error("Error handling experience booking session expired:", error)
  }
}

// Function to find a user by Stripe customer ID
async function getUserByStripeCustomerId(customerId: string) {
  try {
    const adminDb = await getAdminDb()
    if (!adminDb) {
      console.error("Firebase Admin Firestore is not initialized")
      return null
    }

    // Query the userSubscriptions collection for Stripe subscription entries with this customer ID
    const subscriptionsRef = adminDb.collection("userSubscriptions")
    let snapshot = await subscriptionsRef.where("source", "==", "stripe").get()

    // Find the subscription entry with matching customer ID in subscriptionData
    for (const doc of snapshot.docs) {
      const subscriptionData = doc.data()
      if (subscriptionData?.subscriptionData?.customerId === customerId) {
        const userId = subscriptionData.userId
        console.log(`Found user ${userId} in userSubscriptions with customer ID: ${customerId}`)
        return { uid: userId }
      }
    }

    console.log(`No user found with Stripe customer ID: ${customerId}`)
    return null
  } catch (error) {
    console.error("Error finding user by Stripe customer ID:", error)
    return null
  }
}

export async function POST(request: NextRequest) {
  console.log("🔥 WEBHOOK RECEIVED - Starting webhook processing")
  const body = await request.text()
  const signature = request.headers.get("stripe-signature") as string

  console.log("📝 Webhook body length:", body.length)
  console.log("🔑 Signature present:", !!signature)

  if (!signature) {
    console.log("❌ Missing stripe-signature header")
    return NextResponse.json({ error: "Missing stripe-signature header" }, { status: 400 })
  }

  try {
    console.log("🔐 Attempting to verify webhook signature...")
    // Verify the webhook signature
    const event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    )

    console.log("✅ Webhook signature verified successfully")
    console.log("📋 Event type:", event.type)
    console.log("🆔 Event ID:", event.id)

    // Handle the event
    switch (event.type) {
      case "checkout.session.completed": {
        const session = event.data.object as any
        let userId = session.metadata?.userId
        const customerId = session.customer
        const subscriptionId = session.subscription

        console.log(`Checkout session completed: ${session.id}`)
        console.log(`Customer ID: ${customerId}, Subscription ID: ${subscriptionId}`)

        // Check if this is an experience booking
        if (session.metadata?.type === "experience_booking") {
          await handleExperienceBookingCheckoutCompleted(session)
          break
        }

        // Update the user's subscription information
        if (customerId && subscriptionId) {
          // If userId is not in metadata, try to find the user by customer ID
          if (!userId) {
            console.log("No userId in metadata, searching by customer ID")
            const user = await getUserByStripeCustomerId(customerId)
            if (user) {
              userId = user.uid
              console.log(`Found user ${userId} by customer ID ${customerId}`)
            } else {
              console.error(`Could not find user for customer ID ${customerId}`)
              break
            }
          }

          // Get the subscription details
          const subscription = (await stripe.subscriptions.retrieve(
            subscriptionId
          )) as Stripe.Subscription & { current_period_end?: number }

          // Log current_period_end from Stripe (keep as Unix timestamp)
          console.log(
            `Stripe current_period_end:`,
            subscription.current_period_end,
            typeof subscription.current_period_end
          )

          // Add userId to subscription metadata if it's not there
          if (!subscription.metadata?.userId) {
            console.log(`Adding userId ${userId} to subscription metadata`)
            await stripe.subscriptions.update(subscriptionId, {
              metadata: { userId },
            })
          }

          // Get customer details and add userId to customer metadata if not there
          let customer = (await stripe.customers.retrieve(customerId)) as Stripe.Customer
          if (!customer.metadata?.userId) {
            console.log(`Adding userId ${userId} to customer metadata`)
            customer = (await stripe.customers.update(customerId, {
              metadata: { userId },
            })) as Stripe.Customer
          }

          // Use the new flat subscription webhook handler
          const result = await FlatSubscriptionWebhooks.handleSubscriptionCreated(
            subscription,
            customer
          )

          if (!result.success) {
            console.error(
              `Failed to create flat subscription for user ${userId}:`,
              result.error?.message
            )
            // Return error response - no fallback needed
            return NextResponse.json(
              { error: "Failed to process subscription creation", message: result.error?.message },
              { status: 500 }
            )
          } else {
            console.log(`Successfully created flat subscription for user ${userId}`)
          }
        } else {
          console.error("Missing customer ID or subscription ID in checkout session")
        }
        break
      }

      case "customer.subscription.updated": {
        const subscription = event.data.object as any & { current_period_end?: number }
        const customerId = subscription.customer
        const subscriptionId = subscription.id
        const status = subscription.status

        console.log(`Subscription updated: ${subscriptionId}, Status: ${status}`)

        // Log current_period_end from Stripe (keep as Unix timestamp)
        console.log(
          `Stripe current_period_end:`,
          subscription.current_period_end,
          typeof subscription.current_period_end
        )

        // Try to get userId from metadata first
        let userId = subscription.metadata?.userId

        // If not in metadata, find the user by customer ID
        if (!userId) {
          console.log("No userId in metadata, searching by customer ID")
          const user = await getUserByStripeCustomerId(customerId)
          if (user) {
            userId = user.uid
            console.log(`Found user ${userId} by customer ID ${customerId}`)

            // Add userId to subscription metadata for future events
            await stripe.subscriptions.update(subscriptionId, {
              metadata: { userId },
            })
          }
        }

        if (userId) {
          // Get customer details and ensure userId is in metadata
          let customer = (await stripe.customers.retrieve(customerId)) as Stripe.Customer
          if (!customer.metadata?.userId) {
            console.log(`Adding userId ${userId} to customer metadata for update webhook`)
            customer = (await stripe.customers.update(customerId, {
              metadata: { userId },
            })) as Stripe.Customer
          }

          // Use the new flat subscription webhook handler
          const result = await FlatSubscriptionWebhooks.handleSubscriptionUpdated(
            subscription,
            customer
          )

          if (!result.success) {
            console.error(
              `Failed to update flat subscription for user ${userId}:`,
              result.error?.message
            )
            // Return error response - no fallback needed
            return NextResponse.json(
              { error: "Failed to process subscription update", message: result.error?.message },
              { status: 500 }
            )
          } else {
            console.log(
              `Successfully updated flat subscription status to ${status} for user ${userId}`
            )
          }
        } else {
          console.error(`Could not find user for customer ID ${customerId}`)
        }
        break
      }

      case "customer.subscription.deleted": {
        const subscription = event.data.object as any
        const customerId = subscription.customer
        const subscriptionId = subscription.id

        console.log(`Subscription deleted: ${subscriptionId}`)

        // Try to get userId from metadata first
        let userId = subscription.metadata?.userId

        // If not in metadata, find the user by customer ID
        if (!userId) {
          console.log("No userId in metadata, searching by customer ID")
          const user = await getUserByStripeCustomerId(customerId)
          if (user) {
            userId = user.uid
            console.log(`Found user ${userId} by customer ID ${customerId}`)
          }
        }

        if (userId) {
          // Get customer details for the flat subscription webhook
          const customer = (await stripe.customers.retrieve(customerId)) as Stripe.Customer

          // Use the new flat subscription webhook handler with fallback userId
          const result = await FlatSubscriptionWebhooks.handleSubscriptionDeleted(
            subscription,
            customer,
            userId
          )

          if (!result.success) {
            console.error(
              `Failed to delete flat subscription for user ${userId}:`,
              result.error?.message
            )
            // Return error response - no fallback needed
            return NextResponse.json(
              { error: "Failed to process subscription deletion", message: result.error?.message },
              { status: 500 }
            )
          } else {
            console.log(`Successfully deleted flat subscription for user ${userId}`)
          }
        } else {
          console.error(`Could not find user for customer ID ${customerId}`)
        }
        break
      }

      case "invoice.payment_succeeded": {
        const invoice = event.data.object as any
        const customerId = invoice.customer
        const subscriptionId = invoice.subscription

        console.log(`Invoice payment succeeded: ${invoice.id}`)

        if (subscriptionId) {
          const subscription = (await stripe.subscriptions.retrieve(
            subscriptionId
          )) as Stripe.Subscription & { current_period_end?: number }

          // Try to get userId from metadata first
          let userId = subscription.metadata?.userId

          // If not in metadata, find the user by customer ID
          if (!userId) {
            console.log("No userId in metadata, searching by customer ID")
            const user = await getUserByStripeCustomerId(customerId)
            if (user) {
              userId = user.uid
              console.log(`Found user ${userId} by customer ID ${customerId}`)

              // Add userId to subscription metadata for future events
              await stripe.subscriptions.update(subscriptionId, {
                metadata: { userId },
              })
            }
          }

          if (userId) {
            // Log current_period_end from Stripe (keep as Unix timestamp)
            console.log(
              `Stripe current_period_end:`,
              subscription.current_period_end,
              typeof subscription.current_period_end
            )
            // Get customer details for the flat subscription webhook
            const customer = (await stripe.customers.retrieve(customerId)) as Stripe.Customer

            // Use the new flat subscription webhook handler
            const result = await FlatSubscriptionWebhooks.handleInvoicePaymentSucceeded(
              invoice,
              customer
            )

            if (!result.success) {
              console.error(
                `Failed to update flat subscription after payment for user ${userId}:`,
                result.error?.message
              )
              // Return error response - no fallback needed
              return NextResponse.json(
                { error: "Failed to process invoice payment", message: result.error?.message },
                { status: 500 }
              )
            } else {
              console.log(`Successfully updated flat subscription after payment for user ${userId}`)
            }
          } else {
            console.error(`Could not find user for customer ID ${customerId}`)
          }
        } else {
          console.log("No subscription ID in invoice, might be a one-time payment")
        }
        break
      }

      case "invoice.payment_failed": {
        const invoice = event.data.object as any
        const customerId = invoice.customer
        const subscriptionId = invoice.subscription

        console.log(`Invoice payment failed: ${invoice.id}`)

        if (subscriptionId) {
          const subscription = (await stripe.subscriptions.retrieve(
            subscriptionId
          )) as Stripe.Subscription & { current_period_end?: number }

          // Try to get userId from metadata first
          let userId = subscription.metadata?.userId

          // If not in metadata, find the user by customer ID
          if (!userId) {
            console.log("No userId in metadata, searching by customer ID")
            const user = await getUserByStripeCustomerId(customerId)
            if (user) {
              userId = user.uid
              console.log(`Found user ${userId} by customer ID ${customerId}`)

              // Add userId to subscription metadata for future events
              await stripe.subscriptions.update(subscriptionId, {
                metadata: { userId },
              })
            }
          }

          if (userId) {
            // Get customer details for the flat subscription webhook
            const customer = (await stripe.customers.retrieve(customerId)) as Stripe.Customer

            // Use the new flat subscription webhook handler
            const result = await FlatSubscriptionWebhooks.handleInvoicePaymentFailed(
              invoice,
              customer
            )

            if (!result.success) {
              console.error(
                `Failed to update flat subscription to past_due for user ${userId}:`,
                result.error?.message
              )
              // Return error response - no fallback needed
              return NextResponse.json(
                {
                  error: "Failed to process invoice payment failure",
                  message: result.error?.message,
                },
                { status: 500 }
              )
            } else {
              console.log(`Successfully updated flat subscription to past_due for user ${userId}`)
            }
          } else {
            console.error(`Could not find user for customer ID ${customerId}`)
          }
        } else {
          console.log("No subscription ID in invoice")
        }
        break
      }

      case "payment_intent.succeeded": {
        const paymentIntent = event.data.object as Stripe.PaymentIntent
        console.log(`Payment intent succeeded: ${paymentIntent.id}`)

        // Handle experience booking payments
        await handleExperienceBookingPaymentSucceeded(paymentIntent)
        break
      }

      case "payment_intent.payment_failed": {
        const paymentIntent = event.data.object as Stripe.PaymentIntent
        console.log(`Payment intent failed: ${paymentIntent.id}`)

        // Handle experience booking payment failures
        await handleExperienceBookingPaymentFailed(paymentIntent)
        break
      }

      case "checkout.session.expired": {
        const session = event.data.object as Stripe.Checkout.Session
        console.log(`Checkout session expired: ${session.id}`)

        // Handle experience booking session expiration
        await handleExperienceBookingSessionExpired(session)
        break
      }

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    console.log("🎉 Webhook processed successfully, event type:", event.type)
    return NextResponse.json({ received: true, type: event.type })
  } catch (error) {
    console.error("💥 Error handling webhook:", error)

    // Check if this is a webhook signature verification error
    if (error instanceof Stripe.errors.StripeSignatureVerificationError) {
      return NextResponse.json({ error: "Invalid signature" }, { status: 401 })
    }

    const errorMessage = error instanceof Error ? error.message : "Unknown error"

    return NextResponse.json(
      { error: "Failed to handle webhook", message: errorMessage },
      { status: 500 }
    )
  }
}
