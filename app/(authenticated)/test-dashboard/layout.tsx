import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { getAdminAuth } from "@/lib/firebase-admin"
import { Timestamp } from "firebase-admin/firestore"
import { TripServerService } from "@/lib/server/domains/trip/trip.service"
import { UserServerService } from "@/lib/server/domains/user/user.service"
import { SquadServerService } from "@/lib/server/domains/squad/squad.service"
import { DashboardProvider } from "./components/DashboardProvider"
import { DashboardNavigation } from "./components/DashboardNavigation"
import { TestDashboardDemoTour } from "./components/TestDashboardDemoTour"

/**
 * Server-side authentication and data fetching for test dashboard
 */
async function getServerSideData() {
  try {
    // Get the auth token from cookies
    const cookieStore = await cookies()
    const authToken = cookieStore.get("firebase-auth-token")?.value

    if (!authToken) {
      redirect("/login")
    }

    // Verify the session cookie and get user ID
    const adminAuth = await getAdminAuth()
    const decodedToken = await adminAuth.verifySessionCookie(authToken)
    const userId = decodedToken.uid

    console.log("Decoded Token:", decodedToken)
    console.log("User ID:", userId)

    if (!userId) {
      redirect("/login")
    }

    // Fetch user data and dashboard data in parallel
    const [userResult, upcomingTripsResult, pastTripsResult, squadsResult] =
      await Promise.allSettled([
        UserServerService.getUser(userId),
        TripServerService.getUserUpcomingTrips(userId),
        TripServerService.getUserPastTrips(userId),
        SquadServerService.getUserSquads(userId),
      ])

    // Extract results with fallbacks
    const user = userResult.status === "fulfilled" ? userResult.value : null
    const upcomingTrips =
      upcomingTripsResult.status === "fulfilled" ? upcomingTripsResult.value : []
    const pastTrips = pastTripsResult.status === "fulfilled" ? pastTripsResult.value : []
    const squads = squadsResult.status === "fulfilled" ? squadsResult.value : []

    // Debug logging
    console.log("User object:", user)
    console.log("User createdAt type:", typeof user?.createdAt, user?.createdAt)
    console.log("Sample trip:", upcomingTrips[0])
    console.log("Sample squad:", squads[0])

    // Combine all trips
    const allTrips = [...upcomingTrips, ...pastTrips]

    // Create user stats
    const userStats = {
      isAdmin: user?.isAdmin === true,
      isNewUser: user?.newUser === true,
      hasCompletedProfile: Boolean(user?.displayName && user?.email),
      joinDate: user?.createdAt
        ? user.createdAt instanceof Timestamp
          ? user.createdAt.toDate()
          : (user.createdAt as unknown as Date)
        : null,
    }

    // Create squad stats
    const squadStats = {
      totalSquads: squads.length,
      leaderSquads: squads.filter((squad) => squad.leaderId === userId).length,
      memberSquads: squads.filter((squad) => squad.leaderId !== userId).length,
    }

    return {
      user,
      userStats,
      trips: allTrips,
      upcomingTrips,
      pastTrips,
      squads,
      squadStats,
      userId,
    }
  } catch (error) {
    console.error("Error in getServerSideData:", error)
    redirect("/login")
  }
}

/**
 * Test Dashboard Layout with SSR data fetching
 */
export default async function TestDashboardLayout({ children }: { children: React.ReactNode }) {
  const serverData = await getServerSideData()

  return (
    <DashboardProvider initialData={serverData}>
      <div className="min-h-screen bg-background">
        {/* Dashboard Header */}
        <div className="border-b bg-card">
          <div className="container mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold">Test Dashboard</h1>
                <p className="text-muted-foreground">
                  Welcome back, {serverData.user?.displayName || "Friend"}!
                </p>
              </div>
              <div className="text-sm text-muted-foreground">SSR + Realtime Sync</div>
            </div>
          </div>
        </div>

        {/* Dashboard Navigation */}
        <div className="border-b bg-card">
          <div className="container mx-auto px-6">
            <DashboardNavigation />
          </div>
        </div>

        {/* Dashboard Content */}
        <div className="container mx-auto px-6 py-6">{children}</div>

        {/* Demo Tour Modal */}
        <TestDashboardDemoTour />
      </div>
    </DashboardProvider>
  )
}
