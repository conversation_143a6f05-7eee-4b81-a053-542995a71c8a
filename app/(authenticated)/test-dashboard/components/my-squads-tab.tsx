"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Plus, Users, Crown, Calendar } from "lucide-react"
import { Squad } from "@/lib/domains/squad/squad.types"
import { Trip } from "@/lib/domains/trip/trip.types"
import { User } from "@/lib/domains/user/user.types"
import { getBestAvatar, getInitials } from "@/lib/utils/avatar-utils"
import { useDashboard } from "./DashboardProvider"
import { useEffect, useState } from "react"
import { UserService } from "@/lib/domains/user/user.service"
import { SquadService } from "@/lib/domains/squad/squad.service"

// Squad Card Component
function SquadCard({
  squad,
  upcomingTrips,
  leader,
  memberCount,
}: {
  squad: Squad
  upcomingTrips: Trip[]
  leader: User | null
  memberCount: number
}) {
  // Get upcoming trips for this squad
  const squadUpcomingTrips = upcomingTrips.filter((trip) => trip.squadId === squad.id)

  return (
    <Link href={`/squads/${squad.id}`}>
      <Card className="h-full hover:shadow-md transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg flex items-center gap-2">
                {squad.name}
                {leader && (
                  <Badge variant="secondary" className="text-xs">
                    <Crown className="h-3 w-3 mr-1" />
                    Leader
                  </Badge>
                )}
              </CardTitle>
              {squad.description && (
                <CardDescription className="mt-1 line-clamp-2">
                  {squad.description}
                </CardDescription>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Squad Leader */}
          {leader && (
            <div className="flex items-center gap-3">
              <Avatar className="h-8 w-8">
                <AvatarImage
                  src={getBestAvatar(leader.photoURL, leader.displayName, 32)}
                  alt={leader.displayName || "Leader"}
                />
                <AvatarFallback className="text-xs">
                  {getInitials(leader.displayName)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{leader.displayName}</p>
                <p className="text-xs text-muted-foreground">Squad Leader</p>
              </div>
            </div>
          )}

          {/* Squad Stats */}
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-1 text-muted-foreground">
              <Users className="h-4 w-4" />
              <span>{memberCount} members</span>
            </div>
            <div className="flex items-center gap-1 text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span>{squadUpcomingTrips.length} upcoming trips</span>
            </div>
          </div>

          {/* Upcoming Trips Preview */}
          {squadUpcomingTrips.length > 0 && (
            <div className="space-y-2">
              <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                Next Trip
              </p>
              <div className="text-sm">
                <p className="font-medium">{squadUpcomingTrips[0].destination}</p>
                <p className="text-xs text-muted-foreground">
                  {squadUpcomingTrips[0].startDate && 
                    new Date(squadUpcomingTrips[0].startDate.toDate()).toLocaleDateString()
                  }
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </Link>
  )
}

/**
 * My Squads Tab Component
 * Uses SSR data with realtime sync after hydration
 */
export function MySquadsTab() {
  const { squads, upcomingTrips, loading, user } = useDashboard()
  const [squadLeaders, setSquadLeaders] = useState<Record<string, User>>({})
  const [squadMemberCounts, setSquadMemberCounts] = useState<Record<string, number>>({})

  // Fetch squad leaders and member counts
  useEffect(() => {
    if (squads.length === 0) return

    const fetchSquadDetails = async () => {
      const leaders: Record<string, User> = {}
      const memberCounts: Record<string, number> = {}

      await Promise.allSettled(
        squads.map(async (squad) => {
          // Fetch leader if not already cached
          if (squad.leaderId && !squadLeaders[squad.leaderId]) {
            try {
              const leader = await UserService.getUser(squad.leaderId)
              if (leader) {
                leaders[squad.leaderId] = leader
              }
            } catch (error) {
              console.error(`Error fetching leader for squad ${squad.id}:`, error)
            }
          }

          // Fetch member count
          try {
            const members = await SquadService.getSquadMembers(squad.id)
            memberCounts[squad.id] = members.length
          } catch (error) {
            console.error(`Error fetching members for squad ${squad.id}:`, error)
            memberCounts[squad.id] = squad.memberCount || 0
          }
        })
      )

      setSquadLeaders((prev) => ({ ...prev, ...leaders }))
      setSquadMemberCounts((prev) => ({ ...prev, ...memberCounts }))
    }

    fetchSquadDetails()
  }, [squads, squadLeaders])

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">My Squads</h2>
          <p className="text-sm text-muted-foreground">
            {loading.overall ? "Syncing..." : `${squads.length} travel groups`}
          </p>
        </div>
        <Link href="/squads/create">
          <Button>
            <Plus className="mr-2 h-4 w-4" /> Create Squad
          </Button>
        </Link>
      </div>

      {squads.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {squads.map((squad) => (
            <SquadCard
              key={squad.id}
              squad={squad}
              upcomingTrips={upcomingTrips}
              leader={squadLeaders[squad.leaderId] || null}
              memberCount={squadMemberCounts[squad.id] || squad.memberCount || 0}
            />
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <Users className="h-6 w-6 text-primary" />
            </div>
            <p className="font-medium text-center">No Squads Yet</p>
            <p className="text-sm text-muted-foreground text-center mt-1 mb-4">
              Create your first squad to start planning trips with friends
            </p>
            <Link href="/squads/create">
              <Button>Create Your First Squad</Button>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
