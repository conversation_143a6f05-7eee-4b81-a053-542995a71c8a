"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"

const navigationItems = [
  {
    name: "Upcoming Trips",
    href: "/test-dashboard/upcoming-trips",
    description: "View your upcoming adventures",
  },
  {
    name: "My Squads",
    href: "/test-dashboard/my-squads",
    description: "Manage your travel groups",
  },
  {
    name: "Past Trips",
    href: "/test-dashboard/past-trips",
    description: "Relive your memories",
  },
]

/**
 * Dashboard Navigation Component
 * Provides tab-style navigation between dashboard sections
 */
export function DashboardNavigation() {
  const pathname = usePathname()

  return (
    <nav className="flex space-x-8" aria-label="Dashboard navigation">
      {navigationItems.map((item) => {
        const isActive = pathname === item.href
        
        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "flex flex-col items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors",
              isActive
                ? "border-primary text-primary"
                : "border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground"
            )}
            aria-current={isActive ? "page" : undefined}
          >
            <span className="font-semibold">{item.name}</span>
            <span className="text-xs text-muted-foreground mt-1">
              {item.description}
            </span>
          </Link>
        )
      })}
    </nav>
  )
}
