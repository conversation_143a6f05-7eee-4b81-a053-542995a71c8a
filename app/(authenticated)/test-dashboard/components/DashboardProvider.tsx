"use client"

import <PERSON><PERSON>, { create<PERSON>onte<PERSON><PERSON>, use<PERSON>onte<PERSON><PERSON>, useEffect, useState } from "react"
import { User } from "@/lib/domains/user/user.types"
import { Trip } from "@/lib/domains/trip/trip.types"
import { Squad } from "@/lib/domains/squad/squad.types"
import { useRealtimeUserSquads } from "@/lib/domains/squad/squad.realtime.hooks"
import { useRealtimeUserAllTrips } from "@/lib/domains/trip/trip.realtime.hooks"

/**
 * Dashboard data interface
 */
interface DashboardData {
  // User data
  user: User | null
  userStats: {
    isAdmin: boolean
    isNewUser: boolean
    hasCompletedProfile: boolean
    joinDate: Date | null
  }
  
  // Trips data
  trips: Trip[]
  upcomingTrips: Trip[]
  pastTrips: Trip[]
  
  // Squads data
  squads: Squad[]
  squadStats: {
    totalSquads: number
    leaderSquads: number
    memberSquads: number
  }
  
  // Loading states
  loading: {
    trips: boolean
    squads: boolean
    overall: boolean
  }
  
  // Error states
  error: {
    trips: Error | null
    squads: <PERSON>rror | null
  }
  
  // User ID
  userId: string
}

/**
 * Dashboard context
 */
const DashboardContext = createContext<DashboardData | null>(null)

/**
 * Hook to use dashboard data
 */
export function useDashboard() {
  const context = useContext(DashboardContext)
  if (!context) {
    throw new Error("useDashboard must be used within a DashboardProvider")
  }
  return context
}

/**
 * Dashboard Provider Props
 */
interface DashboardProviderProps {
  children: React.ReactNode
  initialData: {
    user: User | null
    userStats: {
      isAdmin: boolean
      isNewUser: boolean
      hasCompletedProfile: boolean
      joinDate: Date | null
    }
    trips: Trip[]
    upcomingTrips: Trip[]
    pastTrips: Trip[]
    squads: Squad[]
    squadStats: {
      totalSquads: number
      leaderSquads: number
      memberSquads: number
    }
    userId: string
  }
}

/**
 * Dashboard Provider Component
 * Manages SSR data and realtime synchronization
 */
export function DashboardProvider({ children, initialData }: DashboardProviderProps) {
  // Initialize state with SSR data
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    ...initialData,
    loading: {
      trips: false,
      squads: false,
      overall: false,
    },
    error: {
      trips: null,
      squads: null,
    },
  })

  // Get realtime data (will sync after hydration)
  const {
    squads: realtimeSquads,
    loading: squadsLoading,
    error: squadsError,
  } = useRealtimeUserSquads()

  const {
    trips: realtimeTrips,
    upcomingTrips: realtimeUpcomingTrips,
    pastTrips: realtimePastTrips,
    loading: tripsLoading,
    error: tripsError,
  } = useRealtimeUserAllTrips()

  // Update dashboard data when realtime data changes
  useEffect(() => {
    setDashboardData((prev) => ({
      ...prev,
      squads: realtimeSquads,
      loading: {
        ...prev.loading,
        squads: squadsLoading,
        overall: squadsLoading || tripsLoading,
      },
      error: {
        ...prev.error,
        squads: squadsError,
      },
    }))
  }, [realtimeSquads, squadsLoading, squadsError])

  useEffect(() => {
    setDashboardData((prev) => ({
      ...prev,
      trips: realtimeTrips,
      upcomingTrips: realtimeUpcomingTrips,
      pastTrips: realtimePastTrips,
      loading: {
        ...prev.loading,
        trips: tripsLoading,
        overall: squadsLoading || tripsLoading,
      },
      error: {
        ...prev.error,
        trips: tripsError,
      },
    }))
  }, [realtimeTrips, realtimeUpcomingTrips, realtimePastTrips, tripsLoading, tripsError, squadsLoading])

  return (
    <DashboardContext.Provider value={dashboardData}>
      {children}
    </DashboardContext.Provider>
  )
}
