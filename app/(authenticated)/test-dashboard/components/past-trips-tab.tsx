"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Users, MapPin, Calendar, Star, Plus } from "lucide-react"
import { OptimizedImage } from "@/components/optimized-image"
import { formatDateRange } from "../../dashboard/components/utils"
import { Trip } from "@/lib/domains/trip/trip.types"
import { Squad } from "@/lib/domains/squad/squad.types"
import { useValidatedTripImageUrl } from "@/lib/hooks/use-validated-image-url"
import { getBestAvatar, getInitials } from "@/lib/utils/avatar-utils"
import { useRealtimeTripsAttendeesDetails } from "@/lib/domains/user-trip/user-trip.realtime.hooks"
import { useDashboard } from "./DashboardProvider"
import { useMemo } from "react"

// Past Trip Card Component
function PastTripCard({
  trip,
  squads,
  tripsAttendeesDetails,
  index = 0,
}: {
  trip: Trip
  squads: Squad[]
  tripsAttendeesDetails: any
  index?: number
}) {
  const { imageUrl, isValidating } = useValidatedTripImageUrl(trip, "400x200", false)

  return (
    <Link href={`/trips/${trip.id}`} key={trip.id}>
      <Card className="h-full hover:shadow-md transition-shadow">
        <div className="aspect-video relative overflow-hidden rounded-t-lg">
          <OptimizedImage
            src={imageUrl}
            alt={trip.destination}
            aspectRatio="video"
            className={`rounded-t-lg transition-opacity duration-300 ${
              isValidating ? "opacity-75" : "opacity-100"
            }`}
            priority={index === 0}
            quality={60}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 33vw, 25vw"
          />
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
            <h3 className="text-white font-bold">{trip.destination}</h3>
            <p className="text-white/80 text-sm">{formatDateRange(trip.startDate, trip.endDate)}</p>
          </div>
          {/* Completed Badge */}
          <div className="absolute top-2 right-2">
            <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200">
              <Star className="h-3 w-3 mr-1" />
              Completed
            </Badge>
          </div>
        </div>
        <CardContent className="pt-4">
          <div className="flex justify-between items-center mb-2">
            <Badge variant="outline">
              {squads.find((s) => s.id === trip.squadId)?.name || "Unknown Squad"}
            </Badge>
            <div className="text-sm text-muted-foreground flex items-center">
              <Users className="h-3 w-3 mr-1" />
              {Array.isArray(trip.attendees) ? trip.attendees.length : 0}
            </div>
          </div>
          
          <div className="space-y-2">
            {/* Trip Status */}
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div className="text-sm">
                <span className="text-muted-foreground">
                  {trip.status === "completed" ? "Trip completed" : "Trip ended"}
                </span>
              </div>
            </div>

            {/* Tasks Completion */}
            <div className="flex items-center gap-2">
              <div className="text-sm">
                <span className="font-medium">{trip.tasksCompleted || 0}</span>
                <span className="text-muted-foreground">
                  {" "}
                  / {trip.totalTasks || 0} tasks completed
                </span>
              </div>
            </div>

            {/* Attendee Avatars */}
            {tripsAttendeesDetails[trip.id] && tripsAttendeesDetails[trip.id].length > 0 && (
              <div className="flex items-center gap-2">
                <div className="flex -space-x-2">
                  {tripsAttendeesDetails[trip.id]
                    .filter((attendee: any) => attendee.status === "going")
                    .slice(0, 4)
                    .map((attendee: any) => (
                      <Avatar key={attendee.userId} className="h-6 w-6 border-2 border-background">
                        <AvatarImage
                          src={getBestAvatar(attendee.user.photoURL, attendee.user.displayName, 24)}
                          alt={attendee.user.displayName || "User"}
                        />
                        <AvatarFallback className="text-xs">
                          {getInitials(attendee.user.displayName)}
                        </AvatarFallback>
                      </Avatar>
                    ))}
                  {tripsAttendeesDetails[trip.id].filter(
                    (attendee: any) => attendee.status === "going"
                  ).length > 4 && (
                    <div className="h-6 w-6 rounded-full bg-muted border-2 border-background flex items-center justify-center">
                      <span className="text-xs text-muted-foreground">
                        +
                        {tripsAttendeesDetails[trip.id].filter(
                          (attendee: any) => attendee.status === "going"
                        ).length - 4}
                      </span>
                    </div>
                  )}
                </div>
                <span className="text-xs text-muted-foreground">
                  traveled together
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}

/**
 * Past Trips Tab Component
 * Uses SSR data with realtime sync after hydration
 */
export function PastTripsTab() {
  const { pastTrips, squads, loading } = useDashboard()

  // Get trip IDs for attendee details
  const pastTripIds = useMemo(() => pastTrips.map((trip) => trip.id), [pastTrips])

  // Get attendee details for past trips
  const { tripsAttendeesDetails } = useRealtimeTripsAttendeesDetails(pastTripIds)

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">Past Trips</h2>
          <p className="text-sm text-muted-foreground">
            {loading.overall ? "Syncing..." : `${pastTrips.length} completed adventures`}
          </p>
        </div>
        <Link href="/trips/create">
          <Button>
            <Plus className="mr-2 h-4 w-4" /> Plan New Trip
          </Button>
        </Link>
      </div>

      {pastTrips.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {pastTrips.map((trip, index) => (
            <PastTripCard
              key={trip.id}
              trip={trip}
              squads={squads}
              tripsAttendeesDetails={tripsAttendeesDetails}
              index={index}
            />
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <MapPin className="h-6 w-6 text-primary" />
            </div>
            <p className="font-medium text-center">No Past Trips</p>
            <p className="text-sm text-muted-foreground text-center mt-1 mb-4">
              Your completed adventures will appear here
            </p>
            <Link href="/trips/create">
              <Button>Plan Your First Trip</Button>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
