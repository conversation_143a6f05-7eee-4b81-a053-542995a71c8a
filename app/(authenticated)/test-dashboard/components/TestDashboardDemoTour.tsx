"use client"

import React, { use<PERSON><PERSON>back, useState, useEffect } from "react"
import { <PERSON>, <PERSON>, <PERSON>rkles } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useDemoTour } from "@/lib/domains/user/user.hooks"
import { toast } from "@/components/ui/use-toast"
import { useDashboard } from "./DashboardProvider"

/**
 * Test Dashboard Demo Tour Component
 * Manages its own state and integrates with the dashboard provider
 */
export function TestDashboardDemoTour() {
  const { user } = useDashboard()
  const { hasOptedOut, checking: demoTourChecking, refreshStatus, optOutOfDemoTour } = useDemoTour()
  const [showDemoTour, setShowDemoTour] = useState(false)
  const [showVideo, setShowVideo] = useState(false)

  // Demo tour logic
  useEffect(() => {
    console.log("Test Dashboard: Demo tour effect triggered", {
      user: !!user,
      hasOptedOut,
      demoTourChecking,
      showDemoTour,
    })

    // Only show demo tour if user is loaded, hasn't opted out, and we're not checking
    if (user && hasOptedOut === false && !demoTourChecking && !showDemoTour) {
      // For new users: show after they complete the trip suggestion flow (when newUser becomes false)
      // For existing users: show on login (when they reach dashboard)
      // The key is that hasOptedOut === false means they haven't seen it or opted out
      console.log("Test Dashboard: Showing demo tour")
      setShowDemoTour(true)
    } else if (hasOptedOut === true && showDemoTour) {
      // If user has opted out, make sure modal is closed
      console.log("Test Dashboard: Hiding demo tour (user opted out)")
      setShowDemoTour(false)
    }
  }, [user, hasOptedOut, demoTourChecking, showDemoTour])

  const handleOptOut = useCallback(async () => {
    try {
      console.log("Test Dashboard Demo tour: Starting opt-out process...")
      const success = await optOutOfDemoTour()
      console.log("Test Dashboard Demo tour: Opt-out result:", success)

      if (success) {
        // Refresh the status to ensure all components know about the change
        console.log("Test Dashboard Demo tour: Refreshing status...")
        await refreshStatus()
        console.log("Test Dashboard Demo tour: Status refreshed")

        toast({
          title: "Demo tour disabled",
          description: "You won't see this tour again. You can always access it from the sidebar.",
        })
        setShowDemoTour(false)
      } else {
        toast({
          title: "Error",
          description: "Failed to save preference. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error opting out of demo tour:", error)
      toast({
        title: "Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      })
    }
  }, [optOutOfDemoTour, refreshStatus])

  // Handle demo tour close - this will be called when user opts out
  const handleDemoTourClose = useCallback(async () => {
    setShowDemoTour(false)
    // Refresh the demo tour status to make sure we have the latest data
    await refreshStatus()
  }, [refreshStatus])

  const handleWatchTour = useCallback(() => {
    setShowVideo(true)
  }, [])

  const handleVideoClose = useCallback(() => {
    setShowVideo(false)
    setShowDemoTour(false)
    // Automatically opt out when they close the video
    handleOptOut()
  }, [handleOptOut])

  if (!showDemoTour) {
    return null
  }

  return (
    <>
      {/* Main Demo Tour Modal */}
      <Dialog open={showDemoTour && !showVideo} onOpenChange={handleDemoTourClose}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                <Sparkles className="h-4 w-4 text-primary" />
              </div>
              <DialogTitle>Welcome to Test Dashboard!</DialogTitle>
            </div>
            <DialogDescription>
              Take a quick tour to see how the new SSR + Realtime dashboard works. This is a test
              implementation showcasing improved performance and maintainability.
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col gap-3 pt-4">
            <Button onClick={handleWatchTour} className="w-full">
              <Play className="mr-2 h-4 w-4" />
              Take the Tour
            </Button>
            <Button variant="outline" onClick={handleOptOut} className="w-full">
              <X className="mr-2 h-4 w-4" />
              Do not show this again
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Video Modal */}
      <Dialog open={showVideo} onOpenChange={handleVideoClose}>
        <DialogContent className="sm:max-w-4xl p-0">
          <div className="relative">
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-2 right-2 z-10 bg-black/50 hover:bg-black/70 text-white"
              onClick={handleVideoClose}
            >
              <X className="h-4 w-4" />
            </Button>
            <div className="aspect-video">
              <iframe
                src="https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1&rel=0&modestbranding=1"
                title="Test Dashboard Tour"
                className="w-full h-full rounded-lg"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
