"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON> } from "next/navigation"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { useRealtimeSquad } from "@/lib/domains/squad/squad.realtime.hooks"
import { useRealtimeUserAIUsage } from "@/lib/domains/user-ai-usage/user-ai-usage.realtime.hooks"
import { AIUsageCategory } from "@/lib/domains/user-ai-usage/user-ai-usage.types"
import { useIsUserSubscribed } from "@/lib/domains/user-subscription/user-subscription.hooks"

import { AIUsageWarning } from "@/components/ai-usage-warning"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Refresh<PERSON><PERSON>, <PERSON>rk<PERSON>, Alert<PERSON>riangle } from "lucide-react"
import { TripSuggestionDetails } from "./trip-suggestion-details"
import { useAITripSuggestionsWithLocation } from "@/lib/domains/ai-suggestions/ai-suggestions-trips-with-location.hooks"
import { CachedTripSuggestion } from "@/lib/domains/ai-suggestions/ai-suggestions-cache.service"
import { useAISuggestionsCache } from "@/lib/domains/ai-suggestions/ai-suggestions-cache.hooks"
import { EnhancedSuggestionCard } from "./enhanced-suggestion-card"
import { LocationPreferenceToggle } from "./location-preference-toggle"
import { LocationSetupDialog } from "./location-setup-dialog"
import { OptionalSuggestionInput } from "@/components/ui/optional-suggestion-input"
import { UserSuggestedBadge } from "@/components/ui/user-suggested-badge"

interface AiTripSuggestionsProps {
  squadId?: string
  onError?: (error: Error) => void
}

export function AiTripSuggestions({ squadId }: AiTripSuggestionsProps) {
  const params = useParams()
  const id = squadId || (params.id as string)
  const currentUser = useUser()
  const isSubscribed = useIsUserSubscribed()
  const { getCategoryUsage } = useRealtimeUserAIUsage(isSubscribed)

  // Get real-time squad data
  const { squad } = useRealtimeSquad(id)

  // Use the new AI trip suggestions hook with location support
  const {
    suggestions,
    loading,
    error,
    usageError,
    suggestionsLoaded,
    usingCachedSuggestions,
    locationPreference,
    hasUserLocation,
    userSuggestion,
    loadSuggestions,
    setLocationPreference,
    setUserSuggestion,
  } = useAITripSuggestionsWithLocation(squadId)

  // Cache checking functionality
  const { hasCachedSuggestions } = useAISuggestionsCache()

  const [refreshing, setRefreshing] = useState(false)
  const [localUsageCount, setLocalUsageCount] = useState<number>(0)
  const [showLocationDialog, setShowLocationDialog] = useState(false)
  const [dialogPreferenceType, setDialogPreferenceType] = useState<"local" | "national">("local")
  const [showCacheWarning, setShowCacheWarning] = useState(false)

  // State for suggestion details modal
  const [selectedSuggestion, setSelectedSuggestion] = useState<CachedTripSuggestion | null>(null)
  const [detailsOpen, setDetailsOpen] = useState(false)

  // Update local usage count when real-time data changes
  useEffect(() => {
    const categoryUsage = getCategoryUsage(AIUsageCategory.TRIP)
    if (categoryUsage) {
      setLocalUsageCount(categoryUsage.count)
    }
  }, [getCategoryUsage])

  // Handle refresh button click
  async function handleRefresh(): Promise<void> {
    setRefreshing(true)
    await loadSuggestions(true)
    setRefreshing(false)
  }

  // Wrapper function for button click events
  const handleFetchClick = async () => {
    // Check if there's existing cache for the current location preference
    if (currentUser?.uid) {
      const cacheKey = `${id}-${locationPreference}`
      const hasExistingCache = hasCachedSuggestions(AIUsageCategory.TRIP, cacheKey, currentUser.uid)

      if (hasExistingCache && !showCacheWarning) {
        // Show warning first
        setShowCacheWarning(true)
        return
      }
    }

    // Proceed with generating suggestions
    const wasUsingCached = usingCachedSuggestions
    await loadSuggestions(false)

    // Hide warning after generating
    setShowCacheWarning(false)

    // If we weren't using cached suggestions, increment the local counter immediately
    // This provides immediate UI feedback while waiting for the real-time update
    if (!wasUsingCached && !isSubscribed) {
      setLocalUsageCount((prev) => prev + 1)
    }
  }

  // Function to proceed with generation after warning
  const handleProceedWithGeneration = async () => {
    setShowCacheWarning(false)
    const wasUsingCached = usingCachedSuggestions
    await loadSuggestions(true) // Force refresh to replace cache

    if (!wasUsingCached && !isSubscribed) {
      setLocalUsageCount((prev) => prev + 1)
    }
  }

  // Function to check if we're in a solo squad (just the current user)
  const isSoloSquad = (): boolean => {
    return !!(squad?.memberCount === 1 && currentUser && squad?.leaderId === currentUser.uid)
  }

  const handleViewDetails = (suggestion: CachedTripSuggestion) => {
    setSelectedSuggestion(suggestion)
    setDetailsOpen(true)
  }

  const handleLocationSetupPrompt = () => {
    // Determine which preference type was clicked based on current state
    const preferenceType = locationPreference === "local" ? "local" : "national"
    setDialogPreferenceType(preferenceType)
    setShowLocationDialog(true)
  }

  return (
    <>
      {selectedSuggestion && (
        <TripSuggestionDetails
          suggestion={selectedSuggestion}
          open={detailsOpen}
          onOpenChange={setDetailsOpen}
          squadId={id}
        />
      )}
      {usageError && usageError.show && (
        <AIUsageWarning
          errorType={usageError.errorType}
          usageData={usageError.usageData}
          onClose={() => {}}
          className="mb-4"
        />
      )}
      {showCacheWarning && (
        <Alert className="mb-4 border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950">
          <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
          <AlertDescription className="text-amber-800 dark:text-amber-200">
            <div className="space-y-3">
              <p>
                You already have cached suggestions for <strong>{locationPreference}</strong>{" "}
                preferences. Generating new suggestions will replace the existing cache.
              </p>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={handleProceedWithGeneration}
                  disabled={loading}
                  className="bg-amber-600 hover:bg-amber-700 text-white"
                >
                  {loading ? (
                    <div className="flex items-center gap-2">
                      <RefreshCw className="h-3 w-3 animate-spin" />
                      <span>Generating...</span>
                    </div>
                  ) : (
                    <>
                      <Sparkles className="h-3 w-3 mr-1" />
                      Generate New Ideas
                    </>
                  )}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setShowCacheWarning(false)}
                  disabled={loading}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      )}
      <Card>
        <CardHeader className="space-y-3">
          {/* Main header row with title and controls */}
          <div className="flex items-start justify-between gap-3">
            <div className="flex-1 min-w-0">
              <CardTitle>Trip Ideas</CardTitle>
              <CardDescription>
                {usingCachedSuggestions
                  ? "Cached trip recommendations based on "
                  : "AI-powered recommendations based on "}
                {isSoloSquad() ? "your" : "squad"} preferences
              </CardDescription>
            </div>

            {/* Controls on the right - always aligned to title row */}
            {suggestionsLoaded && (
              <div className="flex items-center gap-2 flex-shrink-0">
                {!isSubscribed && (
                  <span className="text-xs text-muted-foreground">
                    {localUsageCount}/{getCategoryUsage(AIUsageCategory.TRIP)?.limit || 3}
                  </span>
                )}
                {usingCachedSuggestions && (
                  <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded-md">
                    Cached
                  </span>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={handleRefresh}
                  disabled={loading || refreshing}
                >
                  <RefreshCw className={`h-4 w-4 ${loading || refreshing ? "animate-spin" : ""}`} />
                </Button>
              </div>
            )}
          </div>

          {/* Multi-member squad hint */}
          {squad &&
            squad.memberCount &&
            squad.memberCount > 1 &&
            locationPreference !== "global" && (
              <div className="text-xs text-muted-foreground">
                💡 Global is recommended for multi-member squads
              </div>
            )}
        </CardHeader>
        <CardContent>
          {/* Location Setup Message for Local/National without user location */}
          {(locationPreference === "local" || locationPreference === "national") &&
            !hasUserLocation && (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <div className="mb-4">
                  <div className="text-lg font-medium mb-2">Setup your location</div>
                  <div className="text-muted-foreground mb-4">
                    To get {locationPreference} suggestions, please configure your location in
                    settings.
                  </div>
                </div>
                <Button onClick={() => window.open("/settings", "_blank")} variant="default">
                  Go to Profile Settings
                </Button>
              </div>
            )}

          {/* Regular content when location is available or global is selected */}
          {(locationPreference === "global" ||
            ((locationPreference === "local" || locationPreference === "national") &&
              hasUserLocation)) && (
            <>
              {/* Location Preference Toggle and Generate Button - Mobile: Top, Desktop: Hidden here */}
              <div className="flex flex-col items-center justify-center py-4 gap-4 border-b md:hidden">
                {/* Location Preference Toggle - above the button */}
                <div className="w-full max-w-sm">
                  <LocationPreferenceToggle
                    value={locationPreference}
                    onChange={(preference) => {
                      setLocationPreference(preference)
                      // Don't auto-load suggestions - let user click Generate button
                    }}
                    disabled={loading}
                    hasUserLocation={hasUserLocation}
                    onLocationSetupPrompt={handleLocationSetupPrompt}
                  />
                </div>

                {/* Optional User Suggestion Input */}
                <div className="w-full max-w-sm">
                  <OptionalSuggestionInput
                    value={userSuggestion}
                    onChange={setUserSuggestion}
                    placeholder="Places with great pickle ball courts"
                    label="Specific Interests"
                    maxLength={100}
                    disabled={loading}
                    isProUser={isSubscribed}
                  />
                </div>

                {/* Generate Trip Ideas Button - same width as toggle */}
                <div className="w-full max-w-sm">
                  <Button onClick={handleFetchClick} disabled={loading} className="w-full">
                    {loading ? (
                      <div className="flex items-center justify-center gap-2">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        <span>Loading...</span>
                      </div>
                    ) : (
                      <>
                        <Sparkles className="h-4 w-4 mr-2" />
                        {!isSubscribed && (
                          <span className="mr-2 text-xs">
                            {localUsageCount}/{getCategoryUsage(AIUsageCategory.TRIP)?.limit || 3}
                          </span>
                        )}
                        {currentUser?.uid && suggestions.length > 0
                          ? "Generate New Ideas"
                          : "Generate Trip Ideas"}
                      </>
                    )}
                  </Button>
                </div>
              </div>

              {/* Loading state */}
              {loading && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[...Array(3)].map((_, i) => (
                    <Card key={i}>
                      <div className="aspect-video relative">
                        <Skeleton className="absolute inset-0" />
                      </div>
                      <CardContent className="pt-4">
                        <Skeleton className="h-6 w-3/4 mb-2" />
                        <Skeleton className="h-4 w-1/2 mb-4" />
                        <div className="flex gap-2 mb-4">
                          <Skeleton className="h-5 w-16" />
                          <Skeleton className="h-5 w-16" />
                        </div>
                        <Skeleton className="h-4 w-full mb-1" />
                        <Skeleton className="h-4 w-full mb-1" />
                        <Skeleton className="h-4 w-2/3" />
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </>
          )}

          {/* Error and suggestions sections - only show when location is available or global */}
          {(locationPreference === "global" ||
            ((locationPreference === "local" || locationPreference === "national") &&
              hasUserLocation)) && (
            <>
              {error && (
                <div className="text-center py-8">
                  <p className="text-red-500">{error}</p>
                </div>
              )}
              {suggestionsLoaded && !loading && !error && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {suggestions.length > 0 ? (
                    suggestions.map((suggestion, index) => (
                      <EnhancedSuggestionCard
                        key={index}
                        suggestion={suggestion}
                        index={index}
                        onViewDetails={handleViewDetails}
                      />
                    ))
                  ) : (
                    <div className="col-span-full text-center py-8">
                      <p className="text-muted-foreground">No trip suggestions available</p>
                    </div>
                  )}
                </div>
              )}

              {/* Location Preference Toggle and Generate Button - Desktop: Bottom, Mobile: Hidden here */}
              <div className="hidden md:flex flex-col items-center justify-center py-6 gap-4 border-t mt-6 bg-muted/30">
                {/* Location Preference Toggle - above the button */}
                <div className="w-full max-w-sm">
                  <LocationPreferenceToggle
                    value={locationPreference}
                    onChange={(preference) => {
                      setLocationPreference(preference)
                      // Don't auto-load suggestions - let user click Generate button
                    }}
                    disabled={loading}
                    hasUserLocation={hasUserLocation}
                    onLocationSetupPrompt={handleLocationSetupPrompt}
                  />
                </div>

                {/* Optional User Suggestion Input */}
                <div className="w-full max-w-sm">
                  <OptionalSuggestionInput
                    value={userSuggestion}
                    onChange={setUserSuggestion}
                    placeholder="Places with great pickle ball courts"
                    label="Specific Interests"
                    maxLength={100}
                    disabled={loading}
                    isProUser={isSubscribed}
                  />
                </div>

                {/* Generate Trip Ideas Button - same width as toggle */}
                <div className="w-full max-w-sm">
                  <Button onClick={handleFetchClick} disabled={loading} className="w-full">
                    {loading ? (
                      <div className="flex items-center justify-center gap-2">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        <span>Loading...</span>
                      </div>
                    ) : (
                      <>
                        <Sparkles className="h-4 w-4 mr-2" />
                        {!isSubscribed && (
                          <span className="mr-2 text-xs">
                            {localUsageCount}/{getCategoryUsage(AIUsageCategory.TRIP)?.limit || 3}
                          </span>
                        )}
                        {currentUser?.uid && suggestions.length > 0
                          ? "Generate New Ideas"
                          : "Generate Trip Ideas"}
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Location Setup Dialog */}
      <LocationSetupDialog
        open={showLocationDialog}
        onOpenChange={setShowLocationDialog}
        preferenceType={dialogPreferenceType}
      />
    </>
  )
}
