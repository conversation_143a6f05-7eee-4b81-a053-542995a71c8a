"use server"

import { getAdminDb, getAdminAuth } from "@/lib/firebase-admin"
import { uploadProfilePictureAction } from "@/app/signup/actions/upload-profile-picture"
import { ReferralAdminService } from "@/lib/domains/referral/referral.admin.service"

interface CompleteProfileData {
  userId: string
  email: string
  name: string
  bio?: string
  location?: string
  locationPlaceId?: string
  selectedTravelPreferences: string[]
  budget: string
  selectedAvailability: string[]
  selectedMonths: string[]
  selectedTravelGroups: string[]
  referralCode?: string
  photoURL?: string // From OAuth provider
  authProvider: string // Which OAuth provider was used
}

interface CompleteProfileResult {
  success: boolean
  error?: string
  redirectUrl?: string
}

export async function completeUserProfileAction(
  profileData: CompleteProfileData,
  profilePictureFormData?: FormData
): Promise<CompleteProfileResult> {
  console.log("Complete User Profile Action:", {
    userId: profileData.userId,
    email: profileData.email,
    authProvider: profileData.authProvider,
    hasProfilePicture: !!profilePictureFormData?.get("file"),
  })

  try {
    // Validate required fields
    if (!profileData.userId || !profileData.email || !profileData.name) {
      return {
        success: false,
        error: "Missing required fields: userId, email, and name are required",
      }
    }

    const adminDb = await getAdminDb()
    const adminAuth = await getAdminAuth()
    const userId = profileData.userId
    const userEmail = profileData.email
    const userPhotoURL = profileData.photoURL

    // Check if user document already exists
    const userDocRef = adminDb.collection("users").doc(userId)
    const userDoc = await userDocRef.get()

    if (userDoc.exists) {
      return { success: false, error: "User profile already exists" }
    }

    // Handle profile picture upload if provided
    let profilePictureUrl = userPhotoURL || ""
    if (profilePictureFormData?.get("file")) {
      try {
        const uploadResult = await uploadProfilePictureAction(profilePictureFormData)
        if (uploadResult.success && uploadResult.url) {
          profilePictureUrl = uploadResult.url
        } else {
          console.error("Profile picture upload failed:", uploadResult.error)
          // Continue without profile picture rather than failing the entire process
        }
      } catch (uploadError) {
        console.error("Profile picture upload error:", uploadError)
        // Continue without profile picture rather than failing the entire process
      }
    }

    // Create a batch for atomic operations
    const batch = adminDb.batch()

    // Generate a unique referral code for the new user
    const newUserReferralCode = await ReferralAdminService.generateReferralCode()

    // Create user document
    const userData = {
      uid: userId,
      email: userEmail,
      name: profileData.name,
      displayName: profileData.name, // Add displayName for consistency with Firebase Auth
      bio: profileData.bio || "",
      location: profileData.location || "",
      locationPlaceId: profileData.locationPlaceId || "",
      profilePictureUrl: profilePictureUrl || "",
      photoURL: profilePictureUrl || "", // Add photoURL for consistency with Firebase Auth
      referralCode: newUserReferralCode,
      newUser: true, // Mark as new user
      firstLogin: new Date(), // Add firstLogin timestamp for new user tracking
      demoTourOptedOut: false, // Default to showing demo tour
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // Create user preferences document
    const preferencesData = {
      userId,
      travelPreferences: profileData.selectedTravelPreferences,
      budget: profileData.budget,
      availabilityPreferences: profileData.selectedAvailability,
      preferredTravelSeasons: profileData.selectedMonths,
      travelGroupPreferences: profileData.selectedTravelGroups,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // Create AI usage document
    const aiUsageData = {
      userId,
      itinerarySuggestions: 0,
      restaurantSuggestions: 0,
      activitySuggestions: 0,
      lastResetDate: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // Create subscription document (free tier)
    const subscriptionData = {
      userId,
      plan: "free",
      status: "active",
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // Add documents to batch
    batch.set(userDocRef, userData)
    batch.set(adminDb.collection("userPreferences").doc(userId), preferencesData)
    batch.set(adminDb.collection("userAiUsage").doc(userId), aiUsageData)
    batch.set(adminDb.collection("userSubscriptions").doc(userId), subscriptionData)

    // Update Firebase Auth profile with name and photo
    try {
      await adminAuth.updateUser(userId, {
        displayName: profileData.name,
        photoURL: profilePictureUrl || undefined,
      })
    } catch (authUpdateError) {
      console.error("Firebase Auth profile update failed:", authUpdateError)
      // Continue without failing the entire operation
    }

    // Commit the batch
    await batch.commit()

    // Create referral code for the new user (after user document is created)
    try {
      await ReferralAdminService.createReferralCode(newUserReferralCode, userId)
      console.log("Referral code created for new user:", newUserReferralCode)
    } catch (error) {
      console.error("Error creating referral code for new user:", error)
      // Don't fail the profile completion if referral code creation fails
    }

    // Process referral code if provided
    if (profileData.referralCode) {
      console.log("🎯 Processing referral code:", profileData.referralCode, "for user:", userId)
      try {
        const referralResult = await ReferralAdminService.processReferral(
          profileData.referralCode,
          userId,
          userEmail || ""
        )

        console.log("🔄 Referral processing result:", referralResult)

        if (referralResult.success) {
          console.log("✅ Referral processed successfully:", referralResult)
        } else {
          console.error("❌ Failed to process referral:", referralResult.error)
        }
      } catch (error) {
        console.error("💥 Error processing referral code:", error)
        // Don't fail the profile completion if referral processing fails
      }
    }

    return {
      success: true,
      redirectUrl: "/dashboard",
    }
  } catch (error: any) {
    console.error("Complete user profile error:", error)
    return {
      success: false,
      error: error.message || "Failed to complete profile. Please try again.",
    }
  }
}
