"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Star } from "lucide-react"
import { toast } from "sonner"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { UserLocalExperienceBookingsService } from "@/lib/domains/user/user-local-experience-bookings.service"
import { FeedbackService } from "@/lib/domains/local-experiences/feedback.service"
import { ExperienceBooking } from "@/lib/domains/local-experiences/local-experiences-booking.types"
import {
  UserFeedbackFormData,
  FeedbackRating,
} from "@/lib/domains/local-experiences/feedback.types"
import { PageLoading } from "@/components/page-loading"
import { useAuthStore } from "@/lib/domains/auth/auth.store"

export default function UserFeedbackPage() {
  const params = useParams()
  const router = useRouter()
  const { user, loading: userLoading } = useAuthStore()
  const bookingId = params.bookingId as string

  const [booking, setBooking] = useState<ExperienceBooking | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [hasSubmitted, setHasSubmitted] = useState(false)
  const [formData, setFormData] = useState<UserFeedbackFormData>({
    howDidYouHear: "",
    expectations: "",
    whatWentWell: "",
    whatDidntGoWell: "",
    overallRating: 5,
  })

  // Load booking data and check feedback status
  useEffect(() => {
    const loadBookingData = async () => {
      if (!user || !bookingId) {
        console.log("Feedback page: Missing user or bookingId", { user: !!user, bookingId })
        return
      }

      try {
        setIsLoading(true)
        console.log("Feedback page: Loading booking data", { userId: user.uid, bookingId })

        // Get booking details from user's booking collection
        const bookingResponse = await UserLocalExperienceBookingsService.getUserBooking(
          user.uid,
          bookingId
        )

        console.log("Feedback page: Booking response", {
          success: bookingResponse.success,
          hasData: !!bookingResponse.data,
          error: bookingResponse.error,
        })

        if (!bookingResponse.success || !bookingResponse.data) {
          console.error("Feedback page: Booking not found", bookingResponse.error)
          toast.error(`Booking not found: ${bookingResponse.error || "Unknown error"}`)
          router.push("/experiences?tab=my-bookings")
          return
        }

        const bookingData = bookingResponse.data

        console.log("Feedback page: Booking data", {
          bookingUserId: bookingData.userId,
          currentUserId: user.uid,
          status: bookingData.status,
          experienceId: bookingData.experienceId,
        })

        // Verify this booking belongs to the current user
        if (bookingData.userId !== user.uid) {
          console.error("Feedback page: User mismatch", {
            bookingUserId: bookingData.userId,
            currentUserId: user.uid,
          })
          toast.error("You don't have permission to provide feedback for this booking")
          router.push("/experiences?tab=my-bookings")
          return
        }

        // Check if booking is completed
        if (bookingData.status !== "completed") {
          console.error("Feedback page: Booking not completed", { status: bookingData.status })
          toast.error(
            `You can only provide feedback for completed experiences. Current status: ${bookingData.status}`
          )
          router.push("/experiences?tab=my-bookings")
          return
        }

        setBooking(bookingData)

        // Check if feedback has already been submitted
        const feedbackResponse = await FeedbackService.hasUserSubmittedFeedback(
          bookingData.experienceId,
          bookingId
        )

        if (feedbackResponse.success && feedbackResponse.data) {
          setHasSubmitted(true)
        }
      } catch (error) {
        console.error("Error loading booking data:", error)
        toast.error("Failed to load booking information")
        router.push("/experiences?tab=my-bookings")
      } finally {
        setIsLoading(false)
      }
    }

    if (!userLoading) {
      console.log("Feedback page: Auth state resolved, loading booking data", {
        hasUser: !!user,
        userLoading,
        bookingId,
      })
      loadBookingData()
    } else {
      console.log("Feedback page: Waiting for auth state", { userLoading })
    }
  }, [user, userLoading, bookingId, router])

  const handleRatingChange = (rating: FeedbackRating) => {
    setFormData((prev) => ({ ...prev, overallRating: rating }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user || !booking) return

    // Validate form
    if (!formData.howDidYouHear.trim()) {
      toast.error("Please tell us how you heard about Togeda.ai")
      return
    }

    if (!formData.expectations.trim()) {
      toast.error("Please share your expectations")
      return
    }

    if (!formData.whatWentWell.trim()) {
      toast.error("Please tell us what went well")
      return
    }

    if (!formData.whatDidntGoWell.trim()) {
      toast.error("Please tell us what didn't go well")
      return
    }

    try {
      setIsSubmitting(true)

      const feedbackData = {
        bookingId,
        experienceId: booking.experienceId,
        userId: user.uid,
        userEmail: user.email || "",
        userName: user.displayName || "",
        experienceTitle: booking.experienceTitle,
        experienceDate: booking.date,
        experienceTime: booking.time,
        ...formData,
      }

      const response = await FeedbackService.submitUserFeedback(feedbackData)

      if (response.success) {
        toast.success("Thank you for your feedback!")
        setHasSubmitted(true)
      } else {
        console.error("Error submitting feedback:", response.error)
        toast.error("Failed to submit feedback")
      }
    } catch (error) {
      console.error("Error submitting feedback:", error)
      toast.error("Failed to submit feedback")
    } finally {
      setIsSubmitting(false)
    }
  }

  console.log("Feedback page render state", {
    userLoading,
    isLoading,
    hasUser: !!user,
    hasBooking: !!booking,
    hasSubmitted,
    bookingId,
  })

  if (userLoading || isLoading) {
    console.log("Feedback page: Showing loading state")
    return <PageLoading message="Loading feedback form..." />
  }

  if (!user) {
    console.log("Feedback page: No user, redirecting to login")
    router.push("/auth/login")
    return null
  }

  if (!booking) {
    console.log("Feedback page: No booking data, showing loading")
    return <PageLoading message="Loading booking details..." />
  }

  if (hasSubmitted) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            <Card>
              <CardHeader className="text-center">
                <CardTitle className="text-2xl text-green-600">Thank You!</CardTitle>
                <CardDescription>Your feedback has been submitted successfully</CardDescription>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                <p className="text-muted-foreground">
                  We appreciate you taking the time to share your experience with us. Your feedback
                  helps us improve our services.
                </p>
                <Button onClick={() => router.push("/experiences?tab=my-bookings")}>
                  Back to My Bookings
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl">Experience Feedback</CardTitle>
              <CardDescription>
                {booking.experienceTitle} • {booking.date} at {booking.time}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* How did you hear about Togeda.ai */}
                <div className="space-y-2">
                  <Label htmlFor="howDidYouHear">How did you hear about Togeda.ai?</Label>
                  <Textarea
                    id="howDidYouHear"
                    value={formData.howDidYouHear}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, howDidYouHear: e.target.value }))
                    }
                    placeholder="Tell us how you discovered our platform..."
                    required
                  />
                </div>

                {/* Expectations */}
                <div className="space-y-2">
                  <Label htmlFor="expectations">What were your expectations before going?</Label>
                  <Textarea
                    id="expectations"
                    value={formData.expectations}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, expectations: e.target.value }))
                    }
                    placeholder="Share what you expected from this experience..."
                    required
                  />
                </div>

                {/* What went well */}
                <div className="space-y-2">
                  <Label htmlFor="whatWentWell">What went well with the experience?</Label>
                  <Textarea
                    id="whatWentWell"
                    value={formData.whatWentWell}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, whatWentWell: e.target.value }))
                    }
                    placeholder="Tell us about the positive aspects..."
                    required
                  />
                </div>

                {/* What didn't go well */}
                <div className="space-y-2">
                  <Label htmlFor="whatDidntGoWell">What didn't go well?</Label>
                  <Textarea
                    id="whatDidntGoWell"
                    value={formData.whatDidntGoWell}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, whatDidntGoWell: e.target.value }))
                    }
                    placeholder="Share any areas for improvement..."
                    required
                  />
                </div>

                {/* Overall rating */}
                <div className="space-y-2">
                  <Label>Overall Rating</Label>
                  <div className="flex items-center space-x-1">
                    {[1, 2, 3, 4, 5].map((rating) => (
                      <button
                        key={rating}
                        type="button"
                        onClick={() => handleRatingChange(rating as FeedbackRating)}
                        className="p-1 hover:scale-110 transition-transform"
                      >
                        <Star
                          className={`w-8 h-8 ${
                            rating <= formData.overallRating
                              ? "fill-yellow-400 text-yellow-400"
                              : "text-gray-300"
                          }`}
                        />
                      </button>
                    ))}
                    <span className="ml-2 text-sm text-muted-foreground">
                      {formData.overallRating} star{formData.overallRating !== 1 ? "s" : ""}
                    </span>
                  </div>
                </div>

                <div className="flex gap-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => router.push("/experiences?tab=my-bookings")}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting} className="flex-1">
                    {isSubmitting ? "Submitting..." : "Submit Feedback"}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
