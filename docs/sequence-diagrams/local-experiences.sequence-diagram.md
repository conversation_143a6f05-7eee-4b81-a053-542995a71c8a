# Local Experiences Sequence Diagram

This document provides detailed sequence diagrams showing the system interactions, API calls, and data flow for the local experiences feature.

## Overview

The sequence diagrams illustrate the technical flow between frontend components, backend services, external APIs, and background processes for the complete local experiences lifecycle.

## 1. Experience Discovery and Booking Flow

```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend (React)
    participant LE as LocalExperiencesService
    participant LB as LocalExperiencesBookingService
    participant FS as Firestore
    participant SA as Server Actions
    participant ST as Stripe
    participant FF as Firebase Functions
    participant ES as Email Service

    %% Discovery Phase
    U->>FE: Navigate to /experiences
    FE->>LE: loadAllExperiences()
    LE->>FS: query localExperiences collection
    FS-->>LE: return experiences data
    LE-->>FE: return experiences list
    FE-->>U: Display experience cards

    %% Experience Selection
    U->>FE: Click experience card
    FE->>FE: openExperienceModal()
    FE-->>U: Show experience details modal

    %% Booking Initiation
    U->>FE: Click "Book Now"
    FE->>FE: Show booking form

    %% Date Selection
    U->>FE: Select date
    FE->>LE: getExperienceAvailability(experienceId, date)
    LE->>FS: query availability subcollection
    FS-->>LE: return availability data
    LE-->>FE: return time slots
    FE-->>U: Display available times

    %% Booking Creation
    U->>FE: Fill form and submit
    FE->>LB: createBooking(experienceId, bookingData)

    Note over LB: Validate availability
    LB->>LE: checkTimeSlotAvailability()
    LE->>FS: query existing bookings
    FS-->>LE: return booking count
    LE-->>LB: return availability status

    alt Availability confirmed
        Note over LB: Create booking in transaction
        LB->>FS: transaction.set(experienceBooking)
        LB->>FS: transaction.set(userBooking)
        FS-->>LB: booking created with ID
        LB-->>FE: return bookingId

        %% Payment Processing
        FE->>SA: processPayment(experienceId, bookingId)
        SA->>LB: getBooking(bookingId)
        LB->>FS: get booking details
        FS-->>LB: return booking data
        LB-->>SA: return booking

        SA->>ST: createCheckoutSession()
        ST-->>SA: return session URL
        SA-->>FE: redirect to Stripe
        FE-->>U: Redirect to Stripe checkout

    else No availability
        LB-->>FE: return error
        FE-->>U: Show availability error
    end
```

## 2. Payment and Confirmation Flow

```mermaid
sequenceDiagram
    participant U as User
    participant ST as Stripe Checkout
    participant WH as Stripe Webhooks
    participant SA as Server Actions
    participant FS as Firestore
    participant FF as Firebase Functions
    participant ES as Email Service
    participant FE as Frontend

    %% Payment Process
    U->>ST: Complete payment
    ST->>WH: payment_intent.succeeded webhook

    Note over WH: Webhook validates payment
    WH->>FS: Update booking payment status
    FS->>FF: Trigger onBookingUpdated function

    Note over FF: Detect status change to confirmed + paid
    FF->>FS: Get experience details
    FS-->>FF: Return experience data
    FF->>ES: Send confirmation emails
    ES-->>FF: Email sent confirmation

    %% User Return Flow
    ST-->>U: Redirect to success page
    U->>FE: Land on /experiences/booking/success
    FE->>SA: confirmExperienceBooking(sessionId, bookingId)

    SA->>ST: Retrieve session details
    ST-->>SA: Return session data

    alt Payment verified
        SA->>FS: Update booking to confirmed
        FS-->>SA: Booking updated
        SA-->>FE: Confirmation success
        FE-->>U: Show success message
    else Payment not verified
        SA-->>FE: Return error
        FE-->>U: Show error message
    end
```

## 3. Experience Completion and Feedback Flow

```mermaid
sequenceDiagram
    participant CR as Cron Job
    participant FS as Firestore
    participant FF as Firebase Functions
    participant ES as Email Service
    participant U as User
    participant FE as Frontend
    participant FB as FeedbackService

    %% Automatic Completion Process
    Note over CR: Runs periodically (daily)
    CR->>FS: Query confirmed bookings
    FS-->>CR: Return booking list

    loop For each booking
        CR->>CR: Calculate experience end time
        alt Experience ended
            CR->>FS: Update status to completed
            FS->>FF: Trigger onBookingUpdated
            FF->>ES: Send completion emails
            ES-->>FF: Emails sent
        end
    end

    %% User Feedback Flow
    U->>FE: Click feedback link in email
    FE->>FE: Navigate to /experiences/feedback/[bookingId]

    Note over FE: Load and validate booking
    FE->>LB: getBooking(bookingId)
    LB->>FS: Get booking details
    FS-->>LB: Return booking data
    LB-->>FE: Return booking

    alt Booking validation
        FE->>FB: hasUserSubmittedFeedback(experienceId, bookingId)
        FB->>FS: Check feedback collection
        FS-->>FB: Return feedback status
        FB-->>FE: Return submission status

        alt Not submitted and eligible
            FE-->>U: Show feedback form
            U->>FE: Fill and submit feedback
            FE->>FB: submitUserFeedback(feedbackData)
            FB->>FS: Save feedback document
            FS-->>FB: Feedback saved
            FB-->>FE: Success response
            FE-->>U: Show success message
        else Already submitted
            FE-->>U: Show already submitted message
        end
    else Invalid booking
        FE-->>U: Show error message
    end
```

## 4. Host Feedback Flow

```mermaid
sequenceDiagram
    participant H as Host
    participant FE as Frontend
    participant LES as LocalExperiencesService
    participant LBS as LocalExperiencesBookingService
    participant FB as FeedbackService
    participant FS as Firestore

    %% Host accesses feedback form
    H->>FE: Navigate to /feedback/host/[experienceId]/[bookingId]

    %% Load required data
    par Load experience
        FE->>LES: getExperience(experienceId)
        LES->>FS: Get experience document
        FS-->>LES: Return experience data
        LES-->>FE: Return experience
    and Load booking
        FE->>LBS: getBooking(experienceId, bookingId)
        LBS->>FS: Get booking document
        FS-->>LBS: Return booking data
        LBS-->>FE: Return booking
    end

    %% Validate and show form
    alt Booking is completed
        FE->>FB: hasHostSubmittedFeedback(experienceId, bookingId)
        FB->>FS: Check feedback document
        FS-->>FB: Return feedback status
        FB-->>FE: Return submission status

        alt Not submitted
            FE-->>H: Show host feedback form
            H->>FE: Fill and submit feedback
            FE->>FB: submitHostFeedback(feedbackData)
            FB->>FS: Save host feedback
            FS-->>FB: Feedback saved
            FB-->>FE: Success response
            FE-->>H: Show success message
        else Already submitted
            FE-->>H: Show already submitted message
        end
    else Booking not completed
        FE-->>H: Show error message
    end
```

## 5. Real-time Data Synchronization

```mermaid
sequenceDiagram
    participant FE as Frontend
    participant FS as Firestore
    participant RT as Realtime Listeners
    participant ST as State Management

    %% Setup realtime listeners
    FE->>RT: Setup booking listeners
    RT->>FS: Subscribe to user bookings
    FS-->>RT: Initial data + real-time updates

    %% Handle real-time updates
    loop Real-time updates
        FS->>RT: Booking status changed
        RT->>ST: Update local state
        ST->>FE: Trigger re-render
        FE-->>FE: Update UI with new status
    end

    %% Cleanup on unmount
    FE->>RT: Component unmount
    RT->>FS: Unsubscribe from listeners
```

## Key Technical Interactions

### 1. Booking Creation Transaction

- **Atomic Operation**: Creates booking in both `localExperiences/{id}/bookings` and `users/{id}/localExperienceBookings`
- **Availability Validation**: Checks current bookings vs. max capacity before creation
- **Error Handling**: Rolls back transaction if any step fails

### 2. Payment Processing

- **Server Actions**: Handle Stripe integration securely on server-side
- **Webhook Validation**: Verifies payment completion via Stripe webhooks
- **Dual Updates**: Updates both booking collections atomically

### 3. Status Transitions

- **Firebase Functions**: Trigger on document updates to send notifications
- **Cron Jobs**: Automatically update booking status based on time
- **Real-time Updates**: Sync status changes across all user sessions

### 4. Feedback System

- **Eligibility Checks**: Validates booking completion and ownership
- **Duplicate Prevention**: Checks for existing feedback before allowing submission
- **Structured Storage**: Stores user and host feedback in organized subcollections

## Error Handling Patterns

### 1. Network Failures

- **Retry Logic**: Automatic retries for transient failures
- **Fallback UI**: Show cached data when possible
- **User Feedback**: Clear error messages with retry options

### 2. Validation Errors

- **Client-side**: Immediate feedback on form validation
- **Server-side**: Additional validation before database operations
- **Consistent Messaging**: Standardized error response format

### 3. Payment Failures

- **Webhook Redundancy**: Multiple webhook endpoints for reliability
- **Manual Reconciliation**: Admin tools for payment status verification
- **User Communication**: Clear payment status updates

## Performance Optimizations

### 1. Data Loading

- **Pagination**: Load bookings in batches
- **Caching**: Cache experience data for repeated access
- **Parallel Requests**: Load independent data simultaneously

### 2. Real-time Updates

- **Selective Subscriptions**: Only subscribe to relevant data
- **Debounced Updates**: Prevent excessive re-renders
- **Connection Management**: Proper cleanup of listeners

### 3. Background Processing

- **Batch Operations**: Process multiple bookings in single transaction
- **Scheduled Jobs**: Use cron for time-based operations
- **Async Processing**: Non-blocking email and notification sending
