# Local Experiences User Flow

This document describes the complete user flow for local experiences in the Togeda.ai platform, from discovery to feedback completion.

## Overview

The local experiences feature allows users to discover, book, and participate in curated local experiences. The flow includes discovery, booking, payment, experience participation, and feedback collection.

## User Flow Diagram

```mermaid
flowchart TD
    %% Entry Points
    A[User visits /experiences] --> B{User authenticated?}
    B -->|No| C[Redirect to /auth/login]
    B -->|Yes| D[Load Experiences Page]

    %% Main Experience Page
    D --> E[Show Tabs: Discover - My Bookings]
    E --> F{User selects tab}

    %% Discovery Flow
    F -->|Discover| G[Discovery Tab]
    G --> H[Load all active experiences]
    H --> I[Display experience cards]
    I --> J{User action}

    %% Search and Filter
    J -->|Search| K[Enter search query]
    K --> L[Filter experiences by search term]
    L --> I

    J -->|Filter| M[Open filters modal]
    M --> N[Select categories, price range, etc.]
    N --> O[Apply filters]
    O --> I

    %% Experience Selection
    J -->|Click experience| P[Open Experience Detail Modal]
    P --> Q[Show experience details]
    Q --> R{User action}

    R -->|Close| I
    R -->|Book Now| S[Open Booking Form]

    %% Booking Flow
    S --> T[Experience Booking Form]
    T --> U[Select date using calendar]
    U --> V{Date available?}
    V -->|No| W[Show unavailable message]
    W --> U
    V -->|Yes| X[Load available time slots]
    X --> Y[Select time slot]
    Y --> Z[Enter guest count]
    Z --> AA[Enter contact details]
    AA --> BB[Enter special requests - optional]
    BB --> CC{Form valid?}

    CC -->|No| DD[Show validation errors]
    DD --> T
    CC -->|Yes| EE[Create booking with status: pending]

    %% Payment Flow
    EE --> FF{Booking created?}
    FF -->|No| GG[Show error message]
    GG --> T
    FF -->|Yes| HH[Create Stripe checkout session]
    HH --> II[Redirect to Stripe payment]
    II --> JJ{Payment successful?}

    JJ -->|No| KK[Return to experiences with error]
    KK --> I
    JJ -->|Yes| LL[Stripe webhook updates booking]
    LL --> MM[Status: confirmed, Payment: paid]
    MM --> NN[Firebase function sends confirmation emails]
    NN --> OO[Redirect to success page]
    OO --> PP[Show booking confirmation]
    PP --> QQ[Return to My Bookings]

    %% My Bookings Flow
    F -->|My Bookings| RR[User Bookings Tab]
    RR --> SS[Load user's bookings]
    SS --> TT[Display booking cards]
    TT --> UU{Filter by status?}
    UU -->|Yes| VV[Filter bookings]
    VV --> TT
    UU -->|No| WW[Show all bookings]
    WW --> TT

    %% Booking Status Flow
    TT --> XX{Booking status}
    XX -->|Pending| YY[Show pending payment status]
    XX -->|Confirmed| ZZ[Show confirmed status]
    XX -->|Completed| AAA[Show completed status + feedback option]
    XX -->|Cancelled| BBB[Show cancelled status]

    %% Experience Completion (Background Process)
    ZZ --> CCC[Cron job checks experience end time]
    CCC --> DDD{Experience ended?}
    DDD -->|No| CCC
    DDD -->|Yes| EEE[Update status to completed]
    EEE --> FFF[Firebase function sends completion emails]
    FFF --> AAA

    %% Feedback Flow
    AAA --> GGG{User clicks feedback?}
    GGG -->|No| TT
    GGG -->|Yes| HHH[Navigate to /experiences/feedback/:bookingId]
    HHH --> III[Load feedback form]
    III --> JJJ{Booking eligible?}

    JJJ -->|No - Not completed| KKK[Show error: Only completed experiences]
    JJJ -->|No - Not user's booking| LLL[Show error: No permission]
    JJJ -->|No - Already submitted| MMM[Show already submitted message]
    JJJ -->|Yes| NNN[Show feedback form]

    NNN --> OOO[Fill feedback form]
    OOO --> PPP[Submit feedback]
    PPP --> QQQ{Submission successful?}
    QQQ -->|No| RRR[Show error message]
    RRR --> OOO
    QQQ -->|Yes| SSS[Show success message]
    SSS --> TT

    %% Host Feedback Flow (Parallel)
    FFF --> TTT[Host receives completion email]
    TTT --> UUU[Host clicks feedback link]
    UUU --> VVV[Navigate to /feedback/host/:experienceId/:bookingId]
    VVV --> WWW[Host feedback form]
    WWW --> XXX[Host submits feedback]

    %% Error Handling
    KKK --> TT
    LLL --> TT

    %% Styling
    classDef startEnd fill:#e1f5fe
    classDef process fill:#f3e5f5
    classDef decision fill:#fff3e0
    classDef error fill:#ffebee
    classDef success fill:#e8f5e8

    class A,C,PP,SSS startEnd
    class H,EE,LL,CCC,EEE,III,PPP process
    class B,F,V,CC,FF,JJ,DDD,GGG,JJJ,QQQ decision
    class GG,KK,KKK,LLL,RRR error
    class NN,OO,FFF,SSS success
```

## Key User Paths

### 1. Happy Path - Complete Booking Flow

1. User visits `/experiences`
2. Browses available experiences
3. Selects an experience and opens details
4. Clicks "Book Now"
5. Selects date and time
6. Enters guest details
7. Completes payment via Stripe
8. Receives confirmation email
9. Attends experience
10. Receives completion email with feedback link
11. Submits feedback

### 2. Discovery and Search Path

1. User searches for specific experiences
2. Applies filters (category, price, etc.)
3. Browses filtered results
4. Selects experience to view details

### 3. Booking Management Path

1. User visits "My Bookings" tab
2. Views all their bookings
3. Filters by status if needed
4. Manages upcoming/completed bookings

### 4. Feedback Path

1. Experience is automatically marked as completed
2. User receives completion email
3. User clicks feedback link or navigates from My Bookings
4. Fills out feedback form
5. Submits feedback

## Status Transitions

### Booking Status Flow

- **pending** → **confirmed** (after successful payment)
- **confirmed** → **completed** (after experience end time via cron job)
- **pending/confirmed** → **cancelled** (manual cancellation)

### Payment Status Flow

- **pending** → **paid** (after successful Stripe payment)
- **pending** → **failed** (after failed payment)
- **paid** → **refunded** (after refund processing)

## Key Decision Points

1. **Authentication Check**: Ensures only authenticated users can access experiences
2. **Date Availability**: Validates selected date has available time slots
3. **Form Validation**: Ensures all required booking information is provided
4. **Payment Success**: Confirms payment before booking confirmation
5. **Feedback Eligibility**: Validates booking is completed and belongs to user
6. **Experience Completion**: Automatically determines when experience has ended

## Error Handling

- **Authentication errors**: Redirect to login
- **Availability errors**: Show updated availability
- **Payment errors**: Return to booking form with error message
- **Validation errors**: Highlight invalid fields
- **Permission errors**: Show appropriate error messages
- **Network errors**: Provide retry options

## Background Processes

1. **Experience Completion Cron**: Runs periodically to mark experiences as completed
2. **Email Notifications**: Firebase functions send emails on status changes
3. **Reminder Emails**: Cron job sends reminder emails before experiences
4. **Payment Webhooks**: Stripe webhooks update booking status after payment
