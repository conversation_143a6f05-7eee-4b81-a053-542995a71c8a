import * as functions from "firebase-functions"
import * as admin from "firebase-admin"
import { EmailService } from "../utils/email.service"

/**
 * Interface for experience booking data
 */
interface ExperienceBooking {
  id?: string
  experienceId?: string
  experienceTitle?: string
  experienceLocation?: string
  experienceHost?: string
  userId?: string
  userEmail?: string
  userName?: string
  date: string
  time: string
  availabilityId?: string
  guests?:
    | number
    | Array<{
        name: string
        email?: string
        phone?: string
      }>
  guestCount?: number
  primaryGuest?: {
    name: string
    email: string
    phone?: string
  }
  guestDetails?: Array<{
    name: string
    email?: string
    phone?: string
  }>
  specialRequests?: string
  pricing?: {
    basePrice: number
    guests: number
    subtotal: number
    taxes: number
    fees: number
    total: number
    currency: string
  }
  totalAmount?: number
  currency?: string
  status: "pending" | "confirmed" | "cancelled" | "completed"
  paymentStatus: "pending" | "paid" | "failed" | "refunded"
  stripeSessionId?: string
  stripePaymentIntentId?: string
  paymentIntentId?: string
  stripeCustomerId?: string
  bookedAt?: admin.firestore.Timestamp
  createdAt?: admin.firestore.Timestamp
  updatedAt?: admin.firestore.Timestamp
  confirmedAt?: admin.firestore.Timestamp
  cancelledAt?: admin.firestore.Timestamp
  completedAt?: admin.firestore.Timestamp
  cancellationReason?: string
  emailNotifications?: {
    "7d": boolean
    "3d": boolean
    "1d": boolean
    "2h": boolean
  }
}

/**
 * Interface for local experience data
 */
interface LocalExperience {
  id: string
  title: string
  description: string
  shortDescription: string
  host: {
    name: string
    avatar?: string
    responseTime: string
    languages: string[]
    bio: string
    email?: string
    phone?: string
    internalHostEmail?: string
  }
  location: {
    address: string
    city: string
    state: string
    country: string
    zipCode: string
    coordinates?: {
      latitude: number
      longitude: number
    }
  }
  pricing: {
    basePrice: number
    currency: string
    priceType: "per_person" | "per_group"
  }
  duration: number
  maxGuests: number
  minGuests: number
  categories: string[]
  images: string[]
  inclusions: Array<{
    item: string
    included: boolean
  }>
  cancellationPolicy: string
  rating: number
  reviewCount: number
  isActive: boolean
  stripeProductId?: string
  bookingModel?: "per_session" | "per_max_guest"
  createdAt: admin.firestore.Timestamp
}

/**
 * Determine what type of update occurred to decide which email action to take
 */
function determineUpdateType(
  beforeData: ExperienceBooking,
  afterData: ExperienceBooking
): "booking_confirmation" | "reminder_notification" | "completion" | "no_action" {
  // Check for booking confirmation (payment completed)
  const wasConfirmed = beforeData.status === "confirmed" && beforeData.paymentStatus === "paid"
  const isNowConfirmed = afterData.status === "confirmed" && afterData.paymentStatus === "paid"

  if (!wasConfirmed && isNowConfirmed) {
    return "booking_confirmation"
  }

  // Check for reminder notification updates
  const beforeNotifications = beforeData.emailNotifications || {
    "7d": false,
    "3d": false,
    "1d": false,
    "2h": false,
  }
  const afterNotifications = afterData.emailNotifications || {
    "7d": false,
    "3d": false,
    "1d": false,
    "2h": false,
  }

  const reminderTypes = ["7d", "3d", "1d", "2h"] as const
  for (const type of reminderTypes) {
    if (!beforeNotifications[type] && afterNotifications[type]) {
      return "reminder_notification"
    }
  }

  // Check for completion status change
  const wasCompleted = beforeData.status === "completed"
  const isNowCompleted = afterData.status === "completed"

  if (!wasCompleted && isNowCompleted) {
    return "completion"
  }

  return "no_action"
}

/**
 * Handle booking confirmation emails (existing logic)
 */
async function handleBookingConfirmation(
  bookingData: ExperienceBooking,
  experienceId: string,
  bookingId: string
) {
  console.log(`Booking ${bookingId} confirmed after payment - sending email notifications`)

  // Get the experience details
  const db = admin.firestore()
  const experienceDoc = await db.collection("localExperiences").doc(experienceId).get()

  if (!experienceDoc.exists) {
    console.error(`Experience document not found for experienceId: ${experienceId}`)
    return {
      success: false,
      error: "Experience document not found",
      bookingId,
      experienceId,
    }
  }

  const experienceData = experienceDoc.data() as LocalExperience

  console.log(`Processing email notifications for booking ${bookingId}`)

  // Prioritize guestDetails email over userEmail for contact information
  const guestsArray = Array.isArray(bookingData.guests) ? bookingData.guests : []
  const primaryGuestEmail =
    bookingData.primaryGuest?.email ||
    guestsArray[0]?.email ||
    bookingData.guestDetails?.[0]?.email ||
    bookingData.userEmail
  const primaryGuestName =
    bookingData.primaryGuest?.name ||
    guestsArray[0]?.name ||
    bookingData.guestDetails?.[0]?.name ||
    bookingData.userName
  const primaryGuestPhone =
    bookingData.primaryGuest?.phone ||
    guestsArray[0]?.phone ||
    bookingData.guestDetails?.[0]?.phone ||
    "Not provided"

  // Prepare booking details for emails
  const bookingDetails = {
    bookingId,
    guestName: primaryGuestName || "Guest",
    guestEmail: primaryGuestEmail || "",
    guestPhone: primaryGuestPhone,
    experienceTitle: experienceData.title,
    hostName: experienceData.host.name,
    hostEmail: experienceData.host.email,
    hostPhone: experienceData.host.phone,
    bookingDate: bookingData.date,
    bookingTime: bookingData.time,
    guestCount: bookingData.guestCount || guestsArray.length || 1,
    totalAmount: bookingData.totalAmount || bookingData.pricing?.total || 0,
    specialRequests: bookingData.specialRequests || "None",
    experienceLocation: experienceData.location.address,
    experienceDescription: experienceData.shortDescription,
    cancellationPolicy: experienceData.cancellationPolicy,
  }

  // Send confirmation email to guest with audit email BCC'd
  const auditEmailTo = process.env.AUDIT_EMAIL_TO || "<EMAIL>"
  const guestEmailResult = primaryGuestEmail
    ? await EmailService.sendBookingConfirmationEmail(primaryGuestEmail, bookingDetails, {
        bcc: [auditEmailTo],
      })
    : { success: false, error: "No guest email found" }

  // Send notification email to host (use internal email if available, fallback to public email)
  const hostEmail = experienceData.host.internalHostEmail || experienceData.host.email
  let hostEmailResult: { success: boolean; messageId?: string; error?: string } = {
    success: false,
    error: "No host email configured",
  }

  if (hostEmail) {
    hostEmailResult = await EmailService.sendHostNotificationEmail(hostEmail, {
      ...bookingDetails,
      hostName: experienceData.host.name,
    })
  } else {
    console.warn(`No host email found for experience ${experienceId}`)
  }

  console.log("Email notification results:", {
    guestEmail: guestEmailResult,
    hostEmail: hostEmailResult,
    auditEmailBcc: auditEmailTo,
  })

  return {
    success: true,
    bookingId,
    experienceId,
    guestName: primaryGuestName,
    experienceTitle: experienceData.title,
    emailResults: {
      guestEmailSent: guestEmailResult.success,
      hostEmailSent: hostEmailResult.success,
      guestEmailError: guestEmailResult.error,
      hostEmailError: hostEmailResult.error,
    },
  }
}

/**
 * Handle reminder notification emails
 */
async function handleReminderNotification(
  beforeData: ExperienceBooking,
  afterData: ExperienceBooking,
  experienceId: string,
  bookingId: string
) {
  console.log(`Processing reminder notification for booking ${bookingId}`)

  // Determine which reminder was triggered
  const beforeNotifications = beforeData.emailNotifications || {
    "7d": false,
    "3d": false,
    "1d": false,
    "2h": false,
  }
  const afterNotifications = afterData.emailNotifications || {
    "7d": false,
    "3d": false,
    "1d": false,
    "2h": false,
  }

  let reminderType: "7d" | "3d" | "1d" | "2h" | null = null
  const reminderTypes = ["7d", "3d", "1d", "2h"] as const

  for (const type of reminderTypes) {
    if (!beforeNotifications[type] && afterNotifications[type]) {
      reminderType = type
      break
    }
  }

  if (!reminderType) {
    console.log(`No reminder type detected for booking ${bookingId}`)
    return {
      success: true,
      message: "No reminder notification needed",
      bookingId,
      experienceId,
    }
  }

  // Get the experience details
  const db = admin.firestore()
  const experienceDoc = await db.collection("localExperiences").doc(experienceId).get()

  if (!experienceDoc.exists) {
    console.error(`Experience document not found for experienceId: ${experienceId}`)
    return {
      success: false,
      error: "Experience document not found",
      bookingId,
      experienceId,
    }
  }

  const experienceData = experienceDoc.data() as LocalExperience

  // Prepare reminder details
  console.log(`🔍 === REMINDER EMAIL EXTRACTION DEBUG ===`)
  console.log(`🔍 afterData.primaryGuest:`, afterData.primaryGuest)
  console.log(`🔍 afterData.guests:`, afterData.guests)
  console.log(`🔍 afterData.guestDetails:`, afterData.guestDetails)
  console.log(`🔍 afterData.userEmail:`, afterData.userEmail)

  const afterGuestsArray = Array.isArray(afterData.guests) ? afterData.guests : []
  const primaryGuestEmail =
    afterData.primaryGuest?.email ||
    afterGuestsArray[0]?.email ||
    afterData.guestDetails?.[0]?.email ||
    afterData.userEmail
  const primaryGuestName =
    afterData.primaryGuest?.name ||
    afterGuestsArray[0]?.name ||
    afterData.guestDetails?.[0]?.name ||
    afterData.userName

  console.log(`🔍 Extracted primaryGuestEmail: "${primaryGuestEmail}"`)
  console.log(`🔍 Extracted primaryGuestName: "${primaryGuestName}"`)
  const hostEmail = experienceData.host.internalHostEmail || experienceData.host.email

  const reminderDetails = {
    bookingId,
    guestName: primaryGuestName || "Guest",
    guestEmail: primaryGuestEmail || "",
    experienceTitle: experienceData.title,
    hostName: experienceData.host.name,
    bookingDate: afterData.date,
    bookingTime: afterData.time,
    experienceLocation: experienceData.location.address,
    reminderType,
    guestCount: afterData.guestCount || afterGuestsArray.length || 1,
  }

  console.log(`Sending reminder emails for booking ${bookingId}:`)
  console.log(`  - Primary guest email: "${primaryGuestEmail}" (type: ${typeof primaryGuestEmail})`)
  console.log(`  - Primary guest name: "${primaryGuestName}"`)
  console.log(`  - Host email: "${hostEmail}"`)
  console.log(`  - Reminder type: ${reminderType}`)

  // Send reminder emails only if we have a valid email
  if (!primaryGuestEmail || primaryGuestEmail.trim() === "") {
    console.error(
      `No valid primary guest email found for booking ${bookingId}. Email: "${primaryGuestEmail}"`
    )
    return {
      success: false,
      error: `No valid primary guest email found: "${primaryGuestEmail}"`,
      bookingId,
      experienceId,
    }
  }

  // Ensure email is a string and trimmed
  const cleanEmail = String(primaryGuestEmail).trim()
  console.log(`📧 Cleaned email for user: "${cleanEmail}"`)

  // Send reminder emails
  const results = await Promise.allSettled([
    // Send reminder to user
    EmailService.sendReminderEmail(cleanEmail, reminderDetails, "user"),
    // Send reminder to host (if email available)
    hostEmail
      ? EmailService.sendReminderEmail(hostEmail, reminderDetails, "host")
      : Promise.resolve({ success: false, error: "No host email" }),
  ])

  const userEmailResult =
    results[0].status === "fulfilled"
      ? results[0].value
      : { success: false, error: "Failed to send user reminder" }
  const hostEmailResult =
    results[1].status === "fulfilled"
      ? results[1].value
      : { success: false, error: "Failed to send host reminder" }

  console.log(`${reminderType} reminder emails sent for booking ${bookingId}:`, {
    userEmail: userEmailResult,
    hostEmail: hostEmailResult,
  })

  return {
    success: true,
    bookingId,
    experienceId,
    reminderType,
    emailResults: {
      userEmailSent: userEmailResult.success,
      hostEmailSent: hostEmailResult.success,
      userEmailError: userEmailResult.error,
      hostEmailError: hostEmailResult.error,
    },
  }
}

/**
 * Handle experience completion and feedback request emails
 */
async function handleExperienceCompletion(
  bookingData: ExperienceBooking,
  experienceId: string,
  bookingId: string
) {
  console.log(`Processing completion notification for booking ${bookingId}`)

  // Get the experience details
  const db = admin.firestore()
  const experienceDoc = await db.collection("localExperiences").doc(experienceId).get()

  if (!experienceDoc.exists) {
    console.error(`Experience document not found for experienceId: ${experienceId}`)
    return {
      success: false,
      error: "Experience document not found",
      bookingId,
      experienceId,
    }
  }

  const experienceData = experienceDoc.data() as LocalExperience

  // Prepare feedback request details
  const completionGuestsArray = Array.isArray(bookingData.guests) ? bookingData.guests : []
  const primaryGuestEmail =
    bookingData.primaryGuest?.email ||
    completionGuestsArray[0]?.email ||
    bookingData.guestDetails?.[0]?.email ||
    bookingData.userEmail
  const primaryGuestName =
    bookingData.primaryGuest?.name ||
    completionGuestsArray[0]?.name ||
    bookingData.guestDetails?.[0]?.name ||
    bookingData.userName
  const hostEmail = experienceData.host.internalHostEmail || experienceData.host.email

  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "https://togeda.ai"
  const userFeedbackUrl = `${baseUrl}/experiences/feedback/${bookingId}`
  const hostFeedbackUrl = `${baseUrl}/feedback/host/${experienceId}/${bookingId}`

  const feedbackDetails = {
    bookingId,
    experienceId,
    guestName: primaryGuestName || "Guest",
    guestEmail: primaryGuestEmail || "",
    experienceTitle: experienceData.title,
    hostName: experienceData.host.name,
    bookingDate: bookingData.date,
    bookingTime: bookingData.time,
    experienceLocation: experienceData.location.address,
    userFeedbackUrl,
    hostFeedbackUrl,
  }

  // Send feedback request emails only if we have a valid email
  if (!primaryGuestEmail) {
    console.error(`No primary guest email found for completion feedback ${bookingId}`)
    return {
      success: false,
      error: "No primary guest email found",
      bookingId,
      experienceId,
    }
  }

  // Send feedback request emails
  const results = await Promise.allSettled([
    // Send feedback request to user
    EmailService.sendFeedbackRequestEmail(primaryGuestEmail, feedbackDetails, "user"),
    // Send feedback request to host (if email available)
    hostEmail
      ? EmailService.sendFeedbackRequestEmail(hostEmail, feedbackDetails, "host")
      : Promise.resolve({ success: false, error: "No host email" }),
  ])

  const userEmailResult =
    results[0].status === "fulfilled"
      ? results[0].value
      : { success: false, error: "Failed to send user feedback request" }
  const hostEmailResult =
    results[1].status === "fulfilled"
      ? results[1].value
      : { success: false, error: "Failed to send host feedback request" }

  console.log(`Feedback request emails sent for completed booking ${bookingId}:`, {
    userEmail: userEmailResult,
    hostEmail: hostEmailResult,
  })

  return {
    success: true,
    bookingId,
    experienceId,
    emailResults: {
      userEmailSent: userEmailResult.success,
      hostEmailSent: hostEmailResult.success,
      userEmailError: userEmailResult.error,
      hostEmailError: hostEmailResult.error,
    },
    feedbackUrls: {
      userFeedbackUrl,
      hostFeedbackUrl,
    },
  }
}

/**
 * Firebase Function that triggers when a booking document is updated
 *
 * Specifically handles the booking confirmation flow:
 * 1. Booking is initially created with status "pending" and paymentStatus "pending"
 * 2. After successful Stripe payment, the booking is updated to status "confirmed" and paymentStatus "paid"
 * 3. This function detects that status change and sends email notifications to both guest and host
 *
 * Email notifications are only sent when:
 * - The booking status changes from non-confirmed to confirmed
 * - The payment status is "paid"
 *
 * This ensures emails are sent exactly once when payment is completed.
 */
export const onBookingUpdated = functions.firestore
  .document("localExperiences/{experienceId}/bookings/{bookingId}")
  .onUpdate(
    async (
      change: functions.Change<functions.firestore.QueryDocumentSnapshot>,
      context: functions.EventContext
    ) => {
      try {
        const { experienceId, bookingId } = context.params
        const beforeData = change.before.data() as ExperienceBooking
        const afterData = change.after.data() as ExperienceBooking

        console.log(`Booking updated: ${bookingId} for experience ${experienceId}`)

        // Determine what type of update this is and handle accordingly
        const updateType = determineUpdateType(beforeData, afterData)

        switch (updateType) {
          case "booking_confirmation":
            return await handleBookingConfirmation(afterData, experienceId, bookingId)
          case "reminder_notification":
            return await handleReminderNotification(beforeData, afterData, experienceId, bookingId)
          case "completion":
            return await handleExperienceCompletion(afterData, experienceId, bookingId)
          case "no_action":
          default:
            console.log(`Booking ${bookingId} - no email action needed for this update`)
            return {
              success: true,
              message: "No email notification needed for this update",
              bookingId,
              experienceId,
            }
        }
      } catch (error) {
        console.error("Error in onBookingUpdated function:", error)

        // Don't throw the error to prevent function retries
        // Log the error and return a failure response
        return {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
          bookingId: context.params.bookingId,
          experienceId: context.params.experienceId,
        }
      }
    }
  )
