/**
 * <PERSON><PERSON><PERSON> to fix the existing test booking by adding the id field
 */

import admin from 'firebase-admin';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY || '{}');

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: 'brotrip-mvp'
  });
}

const db = admin.firestore();

async function fixBooking() {
  try {
    const bookingId = 'test-booking-*************';
    const userId = '6oqh095Hj0YcWH1dy8sNhVHouBr1';
    const experienceId = '4DSGuwTKIhaJ9fyuhJ0D';

    console.log('🔧 Fixing existing booking...');
    console.log('  Booking ID:', bookingId);

    const batch = db.batch();

    // Update user booking
    const userBookingRef = db
      .collection('users')
      .doc(userId)
      .collection('localExperienceBookings')
      .doc(bookingId);
    
    batch.update(userBookingRef, { id: bookingId });
    console.log('✅ Will update user booking');

    // Update experience booking
    const expBookingRef = db
      .collection('localExperiences')
      .doc(experienceId)
      .collection('bookings')
      .doc(bookingId);
    
    batch.update(expBookingRef, { id: bookingId });
    console.log('✅ Will update experience booking');

    await batch.commit();
    console.log('\n🎉 Booking fixed successfully!');
    console.log('\n🔗 Test URL:');
    console.log('  http://localhost:3000/experiences/feedback/' + bookingId);
  } catch (error) {
    console.error('❌ Error:', error);
    throw error;
  }
}

fixBooking()
  .then(() => process.exit(0))
  .catch(err => {
    console.error('Failed:', err);
    process.exit(1);
  });

