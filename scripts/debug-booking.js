/**
 * Debug script to check booking data and user authentication
 */

import admin from 'firebase-admin';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY || '{}');

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: 'brotrip-mvp'
  });
}

const db = admin.firestore();

async function debugBooking() {
  try {
    const bookingId = 'test-booking-*************'; // New booking with correct UID
    const userId = 'U8TeGBYI2WV4DuBgXF1Ep24eZQF2'; // Correct <NAME_EMAIL>
    const userEmail = '<EMAIL>';

    console.log('🔍 Debugging booking and user data...\n');

    // 1. Check if user exists in Auth
    try {
      const userRecord = await admin.auth().getUserByEmail(userEmail);
      console.log('✅ User found in Firebase Auth:');
      console.log('  UID:', userRecord.uid);
      console.log('  Email:', userRecord.email);
      console.log('  Display Name:', userRecord.displayName);
      console.log('  Email Verified:', userRecord.emailVerified);
      console.log('');

      if (userRecord.uid !== userId) {
        console.log('⚠️  WARNING: UID mismatch!');
        console.log('  Expected:', userId);
        console.log('  Actual:', userRecord.uid);
        console.log('');
      }
    } catch (error) {
      console.log('❌ User NOT found in Firebase Auth:', error.message);
      console.log('');
    }

    // 2. Check if user document exists
    try {
      const userDoc = await db.collection('users').doc(userId).get();
      if (userDoc.exists) {
        const userData = userDoc.data();
        console.log('✅ User document found in Firestore:');
        console.log('  Email:', userData.email);
        console.log('  Display Name:', userData.displayName);
        console.log('  UID:', userData.uid);
        console.log('');
      } else {
        console.log('❌ User document NOT found in Firestore');
        console.log('');
      }
    } catch (error) {
      console.log('❌ Error checking user document:', error.message);
      console.log('');
    }

    // 3. Check user's booking collection
    try {
      const userBookingRef = db
        .collection('users')
        .doc(userId)
        .collection('localExperienceBookings')
        .doc(bookingId);
      
      const userBookingDoc = await userBookingRef.get();
      
      if (userBookingDoc.exists) {
        const bookingData = userBookingDoc.data();
        console.log('✅ User booking found:');
        console.log('  Document ID:', userBookingDoc.id);
        console.log('  Booking ID field:', bookingData.id);
        console.log('  User ID:', bookingData.userId);
        console.log('  Status:', bookingData.status);
        console.log('  Experience ID:', bookingData.experienceId);
        console.log('  Experience Title:', bookingData.experienceTitle);
        console.log('  Date:', bookingData.date);
        console.log('  Time:', bookingData.time);
        console.log('');

        // Check if all required fields are present
        const requiredFields = ['id', 'userId', 'status', 'experienceId'];
        const missingFields = requiredFields.filter(field => !bookingData[field]);
        
        if (missingFields.length > 0) {
          console.log('⚠️  Missing required fields:', missingFields.join(', '));
        } else {
          console.log('✅ All required fields present');
        }
        console.log('');
      } else {
        console.log('❌ User booking NOT found');
        console.log('  Path:', `users/${userId}/localExperienceBookings/${bookingId}`);
        console.log('');
      }
    } catch (error) {
      console.log('❌ Error checking user booking:', error.message);
      console.log('');
    }

    // 4. Check experience booking collection
    try {
      const experienceId = '4DSGuwTKIhaJ9fyuhJ0D';
      const expBookingRef = db
        .collection('localExperiences')
        .doc(experienceId)
        .collection('bookings')
        .doc(bookingId);
      
      const expBookingDoc = await expBookingRef.get();
      
      if (expBookingDoc.exists) {
        const bookingData = expBookingDoc.data();
        console.log('✅ Experience booking found:');
        console.log('  Document ID:', expBookingDoc.id);
        console.log('  Booking ID field:', bookingData.id);
        console.log('  User ID:', bookingData.userId);
        console.log('  Status:', bookingData.status);
        console.log('');
      } else {
        console.log('❌ Experience booking NOT found');
        console.log('  Path:', `localExperiences/${experienceId}/bookings/${bookingId}`);
        console.log('');
      }
    } catch (error) {
      console.log('❌ Error checking experience booking:', error.message);
      console.log('');
    }

    // 5. Test the exact query that the service uses
    try {
      console.log('🧪 Testing UserLocalExperienceBookingsService.getUserBooking logic...');
      
      const bookingRef = db
        .collection('users')
        .doc(userId)
        .collection('localExperienceBookings')
        .doc(bookingId);
      
      const bookingDoc = await bookingRef.get();
      
      if (!bookingDoc.exists) {
        console.log('❌ Service would return: "Booking not found"');
        console.log('  Reason: Document does not exist');
      } else {
        const booking = {
          id: bookingDoc.id,
          ...bookingDoc.data(),
        };
        
        console.log('✅ Service would return booking successfully');
        console.log('  Final booking object ID:', booking.id);
        console.log('  Final booking object userId:', booking.userId);
        console.log('  Final booking object status:', booking.status);
      }
      console.log('');
    } catch (error) {
      console.log('❌ Service would fail with error:', error.message);
      console.log('');
    }

    console.log('🔗 Test URLs:');
    console.log('  Feedback:', `http://localhost:3000/experiences/feedback/${bookingId}`);
    console.log('  My Bookings:', 'http://localhost:3000/experiences?tab=my-bookings');

  } catch (error) {
    console.error('❌ Debug script error:', error);
  }
}

debugBooking()
  .then(() => process.exit(0))
  .catch(err => {
    console.error('Failed:', err);
    process.exit(1);
  });
