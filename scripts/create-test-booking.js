/**
 * <PERSON><PERSON><PERSON> to create a test booking for feedback testing
 * Run with: node scripts/create-test-booking.js
 */

import admin from "firebase-admin"
import dotenv from "dotenv"

// Load environment variables
dotenv.config({ path: ".env.local" })

// Initialize Firebase Admin
const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY || "{}")

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: "brotrip-mvp",
  })
}

const db = admin.firestore()

async function createTestBooking() {
  try {
    console.log("🚀 Starting test booking creation...\n")

    // User details
    const userId = "6oqh095Hj0YcWH1dy8sNhVHouBr1"
    const userEmail = "<EMAIL>"
    const userName = "DUMMY ACCOUNT00"

    // Experience details (using the Italian Cooking Class)
    const experienceId = "4DSGuwTKIhaJ9fyuhJ0D"
    const experienceTitle = "Authentic Italian Cooking Class"
    const experienceLocation = "San Francisco, United States"
    const experienceHost = "Sofia Benedetti"

    // Booking details - set to yesterday so it's already "completed"
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)
    const bookingDate = yesterday.toISOString().split("T")[0] // YYYY-MM-DD
    const bookingTime = "14:00" // 2 PM

    // Create booking ID
    const bookingId = `test-booking-${Date.now()}`

    // Booking data
    const bookingData = {
      // IMPORTANT: Include the id field in the document data
      id: bookingId,

      // Experience details
      experienceId,
      experienceTitle,
      experienceLocation,
      experienceHost,

      // User details
      userId,
      userEmail,
      userName,

      // Booking details
      date: bookingDate,
      time: bookingTime,
      availabilityId: "slot-14-00",
      guests: 2,
      guestDetails: [
        {
          name: userName,
          email: userEmail,
        },
      ],
      specialRequests: "Test booking for feedback feature",

      // Pricing
      pricing: {
        basePrice: 125,
        guests: 2,
        subtotal: 250,
        taxes: 0,
        fees: 0,
        total: 250,
        currency: "USD",
      },

      // Status - COMPLETED so it's ready for feedback
      status: "completed",
      paymentStatus: "paid",

      // Timestamps
      bookedAt: admin.firestore.Timestamp.now(),
      confirmedAt: admin.firestore.Timestamp.now(),
      completedAt: admin.firestore.Timestamp.now(),
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),

      // Payment details (mock)
      stripeSessionId: "test_session_123",
      stripePaymentIntentId: "test_pi_123",
    }

    console.log("📝 Booking Details:")
    console.log("  User:", userName, `(${userEmail})`)
    console.log("  Experience:", experienceTitle)
    console.log("  Date:", bookingDate, "at", bookingTime)
    console.log("  Status:", bookingData.status)
    console.log("  Booking ID:", bookingId)
    console.log("")

    // Create booking in both locations using a batch
    const batch = db.batch()

    // 1. Create in localExperiences/{experienceId}/bookings/{bookingId}
    const experienceBookingRef = db
      .collection("localExperiences")
      .doc(experienceId)
      .collection("bookings")
      .doc(bookingId)

    batch.set(experienceBookingRef, bookingData)
    console.log(
      "✅ Will create booking in: localExperiences/" + experienceId + "/bookings/" + bookingId
    )

    // 2. Create in users/{userId}/localExperienceBookings/{bookingId}
    const userBookingRef = db
      .collection("users")
      .doc(userId)
      .collection("localExperienceBookings")
      .doc(bookingId)

    batch.set(userBookingRef, bookingData)
    console.log(
      "✅ Will create booking in: users/" + userId + "/localExperienceBookings/" + bookingId
    )

    // Commit the batch
    await batch.commit()
    console.log("\n🎉 Test booking created successfully!\n")

    console.log("📋 Next Steps:")
    console.log("  1. Login as: <EMAIL>")
    console.log("  2. Navigate to: /experiences?tab=my-bookings")
    console.log('  3. Click "Leave Feedback" on the test booking')
    console.log("  4. Or directly visit: /experiences/feedback/" + bookingId)
    console.log("")
    console.log("🔗 Direct Feedback URL:")
    console.log("  http://localhost:3000/experiences/feedback/" + bookingId)
    console.log("")

    return bookingId
  } catch (error) {
    console.error("❌ Error creating test booking:", error)
    throw error
  }
}

// Run the script
createTestBooking()
  .then((bookingId) => {
    console.log("✨ Done! Booking ID:", bookingId)
    process.exit(0)
  })
  .catch((error) => {
    console.error("Failed:", error)
    process.exit(1)
  })
