/**
 * <PERSON><PERSON><PERSON> to create a test booking with the CORRECT <NAME_EMAIL>
 */

import admin from 'firebase-admin';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY || '{}');

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: 'brotrip-mvp'
  });
}

const db = admin.firestore();

async function createCorrectBooking() {
  try {
    // CORRECT user <NAME_EMAIL>
    const userId = 'U8TeGBYI2WV4DuBgXF1Ep24eZQF2'; // Current Firebase Auth UID
    const userEmail = '<EMAIL>';
    const userName = 'TEst Account'; // From Firebase Auth display name

    // Experience details (same as before)
    const experienceId = '4DSGuwTKIhaJ9fyuhJ0D';
    const experienceTitle = 'Authentic Italian Cooking Class';
    const experienceLocation = 'San Francisco, United States';
    const experienceHost = 'Sofia Benedetti';

    // Booking details
    const bookingDate = '2025-09-30'; // Yesterday
    const bookingTime = '14:00';

    // Create booking ID
    const bookingId = `test-booking-${Date.now()}`;

    console.log('🎯 Creating booking with CORRECT UID...');
    console.log('  User UID:', userId);
    console.log('  User Email:', userEmail);
    console.log('  User Name:', userName);
    console.log('  Booking ID:', bookingId);
    console.log('');

    // Booking data
    const bookingData = {
      // IMPORTANT: Include the id field in the document data
      id: bookingId,

      // Experience details
      experienceId,
      experienceTitle,
      experienceLocation,
      experienceHost,

      // User details - CORRECT UID
      userId,
      userEmail,
      userName,

      // Booking details
      date: bookingDate,
      time: bookingTime,
      availabilityId: "slot-14-00",
      guests: 2,
      guestDetails: [
        {
          name: userName,
          email: userEmail,
        },
      ],
      specialRequests: "Test booking for feedback feature - CORRECT UID",

      // Pricing
      pricing: {
        basePrice: 125,
        guests: 2,
        subtotal: 250,
        taxes: 0,
        fees: 0,
        total: 250,
        currency: "USD",
      },

      // Status - COMPLETED so it's ready for feedback
      status: "completed",
      paymentStatus: "paid",

      // Timestamps
      bookedAt: admin.firestore.Timestamp.now(),
      confirmedAt: admin.firestore.Timestamp.now(),
      completedAt: admin.firestore.Timestamp.now(),
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),

      // Payment details (mock)
      stripeSessionId: "test_session_123",
      stripePaymentIntentId: "test_pi_123",
    };

    console.log("📝 Booking Details:");
    console.log("  User:", userName, `(${userEmail})`);
    console.log("  Experience:", experienceTitle);
    console.log("  Date:", bookingDate, "at", bookingTime);
    console.log("  Status:", bookingData.status);
    console.log("  Booking ID:", bookingId);
    console.log("");

    // Create booking in both locations using a batch
    const batch = db.batch();

    // 1. Create in localExperiences/{experienceId}/bookings/{bookingId}
    const experienceBookingRef = db
      .collection("localExperiences")
      .doc(experienceId)
      .collection("bookings")
      .doc(bookingId);

    batch.set(experienceBookingRef, bookingData);
    console.log(
      "✅ Will create booking in: localExperiences/" + experienceId + "/bookings/" + bookingId
    );

    // 2. Create in users/{userId}/localExperienceBookings/{bookingId}
    const userBookingRef = db
      .collection("users")
      .doc(userId)
      .collection("localExperienceBookings")
      .doc(bookingId);

    batch.set(userBookingRef, bookingData);
    console.log(
      "✅ Will create booking in: users/" + userId + "/localExperienceBookings/" + bookingId
    );

    // Commit the batch
    await batch.commit();

    console.log("");
    console.log("🎉 Booking created successfully!");
    console.log("");
    console.log("🔗 Test URLs:");
    console.log("  Feedback:", `http://localhost:3000/experiences/feedback/${bookingId}`);
    console.log("  My Bookings:", "http://localhost:3000/experiences?tab=my-bookings");
    console.log("");
    console.log("📋 Booking Summary:");
    console.log("  Booking ID:", bookingId);
    console.log("  User UID:", userId);
    console.log("  User Email:", userEmail);
    console.log("  Status: completed (ready for feedback)");
    console.log("");
    console.log("🧪 <NAME_EMAIL> and test the feedback page!");

  } catch (error) {
    console.error('❌ Error:', error);
    throw error;
  }
}

createCorrectBooking()
  .then(() => process.exit(0))
  .catch(err => {
    console.error('Failed:', err);
    process.exit(1);
  });
