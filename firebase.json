{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"]}], "hosting": {"public": "auth-hosting", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"]}, "emulators": {"functions": {"port": 5001}, "firestore": {"port": 8080}, "ui": {"enabled": true}, "singleProjectMode": true}}