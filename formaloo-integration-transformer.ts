/**
 * UPDATED ANALYSIS: Formaloo to Experience API Integration
 *
 * After analyzing actual UI usage, many API "required" fields are not critical for <PERSON>.
 * This transformer focuses on fields actually used in the experience display and booking flow.
 *
 * FIELDS ACTUALLY USED IN UI:
 * ✅ Experience Cards: title, images[0], host.name, location.city, duration, maxGuests, pricing.basePrice, pricing.currency
 * ✅ Detail Modal: + description, location.country, minGuests, host.bio (optional), rating, reviewCount
 * ✅ Booking Flow: + availability data, all above fields
 *
 * FIELDS NOT USED IN CURRENT UI:
 * ❌ shortDescription (could use truncated description)
 * ❌ location.address (only city/country shown)
 * ❌ host.responseTime, host.languages (not displayed)
 * ❌ categories (not displayed in current UI)
 * ❌ inclusions (not displayed in current UI)
 * ❌ cancellationPolicy (not displayed in current UI)
 */

interface FormalooResponse {
  data: {
    rows: Array<{
      data: Record<string, any>
      rendered_data: Array<{
        slug: string
        title: string
        value: any
        raw_value: any
      }>
    }>
  }
}

interface TransformationResult {
  success: boolean
  data?: any
  errors: string[]
  warnings: string[]
}

// Day slug mapping from Formaloo form
const DAY_SLUG_MAPPING = {
  "8RgSOWGK": "sunday",
  ydersbLY: "monday",
  XMawvFz1: "tuesday",
  kMqh5x4r: "wednesday",
  PxUQpaso: "thursday",
  DQIJlz1V: "friday",
  aNDQM0nB: "saturday",
} as const

export class FormalooTransformer {
  /**
   * Transform Formaloo response to Experience API format
   */
  static transform(formalooResponse: FormalooResponse): TransformationResult {
    const errors: string[] = []
    const warnings: string[] = []

    try {
      const row = formalooResponse.data.rows[0]
      if (!row) {
        return { success: false, errors: ["No form data found"], warnings: [] }
      }

      const data = row.data

      // Extract basic fields
      const title = data.BqeyVEvq || ""
      const description = data.iiGea5NY || ""
      const basePrice = typeof data.xpvaozke === "number" ? data.xpvaozke : 0
      const maxGuests = typeof data.AAqrHN7E === "number" ? data.AAqrHN7E : 0
      const minGuests = parseInt(data.VFJCVgcU) || 1

      // Validate required fields
      if (!title) errors.push("Experience title is required")
      if (!description) errors.push("Experience description is required")
      if (basePrice <= 0) errors.push("Valid price is required")
      if (maxGuests <= 0) errors.push("Valid max guests is required")

      // Extract images
      const images = this.extractImages(data)
      if (images.length === 0) {
        errors.push("At least one image is required")
      }

      // Extract host information
      const hostName = data.A8AGLqvt || ""
      const hostEmail = data.BIty7HNh || ""
      const hostPhone = data.UaULIPVA || ""

      if (!hostName) errors.push("Host name is required")
      if (!hostEmail) warnings.push("Host email not provided")

      // Extract availability
      const availability = this.transformAvailability(data)
      if (!availability.success) {
        errors.push(...availability.errors)
      }

      // Build experience data with defaults for missing fields
      const experienceData = {
        title,
        description,
        shortDescription: description.substring(0, 100) + "...", // Default: truncated description
        host: {
          name: hostName,
          email: hostEmail,
          phone: hostPhone,
          responseTime: "Usually responds within a few hours", // Default
          languages: ["English"], // Default
          bio: `Experienced host offering ${title}`, // Default
          internalHostEmail: hostEmail,
        },
        location: {
          address: "Address to be provided", // TODO: Add to form
          city: "City to be provided", // TODO: Add to form
          country: "Country to be provided", // TODO: Add to form
        },
        pricing: {
          basePrice,
          currency: "USD", // Default
        },
        duration: 120, // Default: 2 hours (TODO: Add to form)
        maxGuests,
        minGuests,
        categories: ["adventure"], // Default (TODO: Add to form)
        images,
        inclusions: [
          // Default (TODO: Add to form)
          { item: "Professional guide", included: true },
          { item: "Safety equipment", included: true },
          { item: "Transportation", included: false },
        ],
        cancellationPolicy: "Free cancellation up to 24 hours before the experience", // Default
        isActive: true,
        bookingModel: "per_max_guest",
        availability: availability.success ? availability.data : undefined,
      }

      return {
        success: errors.length === 0,
        data: experienceData,
        errors,
        warnings,
      }
    } catch (error) {
      return {
        success: false,
        errors: [`Transformation error: ${error.message}`],
        warnings,
      }
    }
  }

  /**
   * Extract and validate image URLs
   */
  private static extractImages(data: Record<string, any>): string[] {
    const images: string[] = []

    // Photo 1 (required)
    if (data.yD6EROaP && typeof data.yD6EROaP === "string") {
      images.push(data.yD6EROaP)
    }

    // Photo 2 (optional)
    if (data["1WEHw2qp"] && typeof data["1WEHw2qp"] === "string") {
      images.push(data["1WEHw2qp"])
    }

    return images.filter((url) => url && url.startsWith("http"))
  }

  /**
   * Transform availability data from Formaloo format
   */
  private static transformAvailability(data: Record<string, any>): {
    success: boolean
    data?: any
    errors: string[]
  } {
    const errors: string[] = []

    try {
      const selectedDays = data.XLbI01J5 || [] // Array of day slugs
      const availabilityData = data["1eUzcbfh"] || [] // Repeating section data

      if (!Array.isArray(selectedDays) || selectedDays.length === 0) {
        return { success: false, errors: ["No available days selected"] }
      }

      if (!Array.isArray(availabilityData)) {
        return { success: false, errors: ["Invalid availability data format"] }
      }

      const weeklySchedule: Record<string, any[]> = {}

      // Process each availability entry
      for (const entry of availabilityData) {
        const dayName = entry.WqoyOoud?.trim()
        const startTime = entry.Wkp5pYJl
        const endTime = entry.Dyan2EKX

        if (!dayName) continue

        // Map day name to lowercase
        const normalizedDay = dayName.toLowerCase().replace(/\s+/g, "")

        // Skip if no valid times
        if (!startTime || !endTime) {
          continue
        }

        // Create time slot
        const timeSlot = {
          time: startTime,
          available: true,
          maxGuests: 8, // Default, could be made configurable
        }

        if (!weeklySchedule[normalizedDay]) {
          weeklySchedule[normalizedDay] = []
        }

        weeklySchedule[normalizedDay].push(timeSlot)
      }

      return {
        success: true,
        data: {
          weeklySchedule,
        },
      }
    } catch (error) {
      return {
        success: false,
        errors: [`Availability transformation error: ${error.message}`],
      }
    }
  }
}

/**
 * Usage example:
 *
 * const result = FormalooTransformer.transform(formalooResponse)
 *
 * if (result.success) {
 *   // Send to experience creation API
 *   const response = await fetch('/api/admin/experiences/create', {
 *     method: 'POST',
 *     headers: {
 *       'Authorization': `Bearer ${API_KEY}`,
 *       'Content-Type': 'application/json'
 *     },
 *     body: JSON.stringify(result.data)
 *   })
 * } else {
 *   console.error('Transformation failed:', result.errors)
 * }
 */
