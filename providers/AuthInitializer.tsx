"use client"

import { useEffect } from "react"
import { useAuthStore } from "@/lib/domains/auth/auth.store"
import { AuthRealtimeService } from "@/lib/domains/auth/auth.realtime.service"
import { AuthCookieService } from "@/lib/client/auth/auth-cookie.service"

export default function AuthInitializer() {
  const { setUser, setLoading } = useAuthStore()

  useEffect(() => {
    setLoading(true)
    const unsubscribe = AuthRealtimeService.subscribeToAuthState((user, error) => {
      if (error) console.error(error)

      // Set auth cookie for SSR
      AuthCookieService.setAuthCookie(user)

      setUser(user)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [setUser, setLoading])

  return null
}
